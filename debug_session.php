<?php
// Debug script to check session status and clear problematic sessions
session_start();

echo "<h2>Session Debug Information</h2>";
echo "<p><strong>Session ID:</strong> " . session_id() . "</p>";
echo "<p><strong>Session Status:</strong> " . session_status() . "</p>";
echo "<p><strong>Session Data:</strong></p>";
echo "<pre>";
print_r($_SESSION);
echo "</pre>";

echo "<h3>Actions:</h3>";
echo "<a href='?action=clear'>Clear Session</a> | ";
echo "<a href='?action=destroy'>Destroy Session</a> | ";
echo "<a href='appp/signin/index'>Go to Signin</a>";

if (isset($_GET['action'])) {
    if ($_GET['action'] == 'clear') {
        $_SESSION = array();
        echo "<p style='color: green;'>Session cleared!</p>";
    } elseif ($_GET['action'] == 'destroy') {
        session_destroy();
        echo "<p style='color: green;'>Session destroyed!</p>";
    }
}

echo "<h3>Cookies:</h3>";
echo "<pre>";
print_r($_COOKIE);
echo "</pre>";
?>
