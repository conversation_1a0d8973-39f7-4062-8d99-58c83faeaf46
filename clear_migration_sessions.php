<?php
// Clear migration sessions script
echo "<h2>Clear Migration Sessions</h2>";
echo "<p>This script will clear any problematic sessions left over from the cPanel to CloudPanel migration.</p>";

if (isset($_GET['clear'])) {
    echo "<h3>Clearing Sessions...</h3>";
    
    // Method 1: Clear file-based sessions
    $cache_dir = 'appp/mvc/cache/';
    if (is_dir($cache_dir)) {
        $files = glob($cache_dir . 'ci_session*');
        $count = 0;
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
                $count++;
            }
        }
        echo "<p>✓ Cleared $count file-based session files</p>";
    }
    
    // Method 2: Clear database sessions (if using database)
    if (file_exists('appp/mvc/config/production/database.php')) {
        include 'appp/mvc/config/production/database.php';
        
        $connection = @new mysqli($db['default']['hostname'], $db['default']['username'], $db['default']['password'], $db['default']['database']);
        
        if (!$connection->connect_error) {
            $result = $connection->query("SHOW TABLES LIKE 'school_sessions'");
            if ($result && $result->num_rows > 0) {
                $delete_result = $connection->query("DELETE FROM school_sessions WHERE timestamp < " . (time() - 7200));
                if ($delete_result) {
                    echo "<p>✓ Cleared old database sessions</p>";
                } else {
                    echo "<p>✗ Failed to clear database sessions: " . $connection->error . "</p>";
                }
            }
            $connection->close();
        }
    }
    
    // Method 3: Clear current PHP session
    session_start();
    session_destroy();
    
    // Clear cookies
    if (isset($_SERVER['HTTP_COOKIE'])) {
        $cookies = explode(';', $_SERVER['HTTP_COOKIE']);
        foreach($cookies as $cookie) {
            $parts = explode('=', $cookie);
            $name = trim($parts[0]);
            setcookie($name, '', time()-1000);
            setcookie($name, '', time()-1000, '/');
        }
    }
    
    echo "<p>✓ Cleared PHP sessions and cookies</p>";
    echo "<p style='color: green; font-weight: bold;'>✓ All migration sessions cleared!</p>";
    echo "<p><a href='index.php'>Test the main site now</a></p>";
    
} else {
    echo "<h3>What this will do:</h3>";
    echo "<ul>";
    echo "<li>Clear all file-based session files</li>";
    echo "<li>Clear old database session records</li>";
    echo "<li>Clear current PHP session</li>";
    echo "<li>Clear browser cookies</li>";
    echo "</ul>";
    
    echo "<p><strong>Warning:</strong> This will log out all current users.</p>";
    echo "<a href='?clear=1' style='background: #dc3545; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Clear All Sessions</a>";
}

echo "<hr>";
echo "<p><a href='cloudpanel_migration_fix.php'>Back to Migration Fix</a> | <a href='test_redirect.php'>Test Redirects</a></p>";
?>
