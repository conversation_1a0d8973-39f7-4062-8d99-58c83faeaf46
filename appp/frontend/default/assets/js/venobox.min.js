/*
 * VenoBox - jQuery Plugin
 * version: 1.6.0
 * @requires jQuery
 *
 * Examples at http://lab.veno.it/venobox/
 * License: MIT License
 * License URI: https://github.com/nicolafranchini/VenoBox/blob/master/LICENSE
 * Copyright 2013-2015 <PERSON> - @nicolafranchini
 *
 */
!function(t){function a(){t.ajax({url:g,cache:!1}).done(function(t){u.html('<div class="vbox-inline">'+t+"</div>"),l(!0)}).fail(function(){u.html('<div class="vbox-inline"><p>Error retrieving contents, please retry</div>'),l(!0)})}function e(){u.html('<iframe class="venoframe" src="'+g+'"></iframe>'),l()}function o(t){var a=g.split("/"),e=a[a.length-1],o=t?"?autoplay=1":"";u.html('<iframe class="venoframe" webkitallowfullscreen mozallowfullscreen allowfullscreen frameborder="0" src="//player.vimeo.com/video/'+e+o+'"></iframe>'),l()}function i(t){var a=g.split("/"),e=a[a.length-1],o=t?"?autoplay=1":"";u.html('<iframe class="venoframe" webkitallowfullscreen mozallowfullscreen allowfullscreen src="//www.youtube.com/embed/'+e+o+'"></iframe>'),l()}function n(){u.html('<div class="vbox-inline">'+t(g).html()+"</div>"),l()}function r(){Q=t(".vbox-content").find("img"),Q.one("load",function(){l()}).each(function(){this.complete&&t(this).load()})}function l(){f.html(X),u.find(">:first-child").addClass("figlio"),t(".figlio").css("width",C).css("height",j).css("padding",m).css("background",s),I=u.outerHeight(),G=t(window).height(),G>I+80?(D=(G-I)/2,u.css("margin-top",D),u.css("margin-bottom",D)):(u.css("margin-top","40px"),u.css("margin-bottom","40px")),u.animate({opacity:"1"},"slow")}function d(){t(".vbox-content").length&&(I=u.height(),G=t(window).height(),G>I+80?(D=(G-I)/2,u.css("margin-top",D),u.css("margin-bottom",D)):(u.css("margin-top","40px"),u.css("margin-bottom","40px")))}var c,s,v,f,m,b,h,u,g,p,x,y,w,k,C,j,q,z,H,D,E,L,P,Q,X,A,B,F,G,I,J,K;t.fn.extend({venobox:function(l){var d={framewidth:"",frameheight:"",border:"0",bgcolor:"#fff",titleattr:"title",numeratio:!1,infinigall:!1,overlayclose:!0},D=t.extend(d,l);return this.each(function(){var l=t(this);return l.data("venobox")?!0:(l.addClass("vbox-item"),l.data("framewidth",D.framewidth),l.data("frameheight",D.frameheight),l.data("border",D.border),l.data("bgcolor",D.bgcolor),l.data("numeratio",D.numeratio),l.data("infinigall",D.infinigall),l.data("overlayclose",D.overlayclose),l.data("venobox",!0),void l.click(function(d){function Q(){A=l.data("gall"),E=l.data("numeratio"),q=l.data("infinigall"),z=t('.vbox-item[data-gall="'+A+'"]'),z.length>1&&E===!0?(v.html(z.index(l)+1+" / "+z.length),v.show()):v.hide(),B=z.eq(z.index(l)+1),F=z.eq(z.index(l)-1),l.attr(D.titleattr)?(X=l.attr(D.titleattr),f.show()):(X="",f.hide()),z.length>1&&q===!0?(J=!0,K=!0,B.length<1&&(B=z.eq(0)),z.index(l)<1&&(F=z.eq(z.index(z.length)))):(B.length>0?(t(".vbox-next").css("display","block"),J=!0):(t(".vbox-next").css("display","none"),J=!1),z.index(l)>0?(t(".vbox-prev").css("display","block"),K=!0):(t(".vbox-prev").css("display","none"),K=!1))}function G(t){27===t.keyCode&&I()}function I(){t("body").removeClass("vbox-open"),t("body").unbind("keydown",G),P.animate({opacity:0},500,function(){P.remove(),H=!1,l.focus()})}d.stopPropagation(),d.preventDefault(),l=t(this),L=l.data("overlay"),C=l.data("framewidth"),j=l.data("frameheight"),c=l.data("autoplay")||!1,m=l.data("border"),s=l.data("bgcolor"),J=!1,K=!1,H=!1,g=l.data("href")||l.attr("href"),w=l.data("css")||"",t("body").addClass("vbox-open"),b='<div class="vbox-overlay '+w+'" style="background:'+L+'"><div class="vbox-preloader">Loading...</div><div class="vbox-container"><div class="vbox-content"></div></div><div class="vbox-title"></div><div class="vbox-num">0/0</div><div class="vbox-close">X</div><div class="vbox-next">next</div><div class="vbox-prev">prev</div></div>',t("body").append(b),P=t(".vbox-overlay"),h=t(".vbox-container"),u=t(".vbox-content"),v=t(".vbox-num"),f=t(".vbox-title"),u.html(""),u.css("opacity","0"),Q(),P.css("min-height",t(window).outerHeight()),P.animate({opacity:1},250,function(){"iframe"==l.data("type")?e():"inline"==l.data("type")?n():"ajax"==l.data("type")?a():"vimeo"==l.data("type")?o(c):"youtube"==l.data("type")?i(c):(u.html('<img src="'+g+'">'),r())});var M={prev:function(){H||(H=!0,L=F.data("overlay"),C=F.data("framewidth"),j=F.data("frameheight"),m=F.data("border"),s=F.data("bgcolor"),g=F.data("href")||F.attr("href"),c=F.data("autoplay"),X=F.attr(D.titleattr)?F.attr(D.titleattr):"",void 0===L&&(L=""),u.animate({opacity:0},500,function(){P.css("background",L),"iframe"==F.data("type")?e():"inline"==F.data("type")?n():"ajax"==F.data("type")?a():"youtube"==F.data("type")?i(c):"vimeo"==F.data("type")?o(c):(u.html('<img src="'+g+'">'),r()),l=F,Q(),H=!1}))},next:function(){H||(H=!0,L=B.data("overlay"),C=B.data("framewidth"),j=B.data("frameheight"),m=B.data("border"),s=B.data("bgcolor"),g=B.data("href")||B.attr("href"),c=B.data("autoplay"),X=B.attr(D.titleattr)?B.attr(D.titleattr):"",void 0===L&&(L=""),u.animate({opacity:0},500,function(){P.css("background",L),"iframe"==B.data("type")?e():"inline"==B.data("type")?n():"ajax"==B.data("type")?a():"youtube"==B.data("type")?i(c):"vimeo"==B.data("type")?o(c):(u.html('<img src="'+g+'">'),r()),l=B,Q(),H=!1}))}};t("body").keydown(function(t){37==t.keyCode&&1==K&&M.prev(),39==t.keyCode&&1==J&&M.next()}),t(".vbox-prev").click(function(){M.prev()}),t(".vbox-next").click(function(){M.next()});var N=".vbox-close, .vbox-overlay";return l.data("overlayclose")||(N=".vbox-close"),t(N).click(function(a){p=".figlio",y=".vbox-prev",x=".vbox-next",k=".figlio *",t(a.target).is(p)||t(a.target).is(x)||t(a.target).is(y)||t(a.target).is(k)||I()}),t("body").keydown(G),!1}))})}}),t(window).resize(function(){d()})}(jQuery);
