/*!
 * SlickNav Responsive Mobile Menu v1.0.8
 * (c) 2016 <PERSON>
 * licensed under MIT
 */
!function(e,n,t){function a(n,t){this.element=n,this.settings=e.extend({},i,t),this.settings.duplicate||t.hasOwnProperty("removeIds")||(this.settings.removeIds=!1),this._defaults=i,this._name=s,this.init()}var i={label:"MENU",duplicate:!0,duration:200,easingOpen:"swing",easingClose:"swing",closedSymbol:"&#9658;",openedSymbol:"&#9660;",prependTo:"body",appendTo:"",parentTag:"a",closeOnClick:!1,allowParentLinks:!1,nestedParentLinks:!0,showChildren:!1,removeIds:!0,removeClasses:!1,removeStyles:!1,brand:"",animations:"jquery",init:function(){},beforeOpen:function(){},beforeClose:function(){},afterOpen:function(){},afterClose:function(){}},s="slicknav",o="slicknav";a.prototype.init=function(){var t,a,i=this,s=e(this.element),l=this.settings;if(l.duplicate?i.mobileNav=s.clone():i.mobileNav=s,l.removeIds&&(i.mobileNav.removeAttr("id"),i.mobileNav.find("*").each(function(n,t){e(t).removeAttr("id")})),l.removeClasses&&(i.mobileNav.removeAttr("class"),i.mobileNav.find("*").each(function(n,t){e(t).removeAttr("class")})),l.removeStyles&&(i.mobileNav.removeAttr("style"),i.mobileNav.find("*").each(function(n,t){e(t).removeAttr("style")})),t=o+"_icon",""===l.label&&(t+=" "+o+"_no-text"),"a"==l.parentTag&&(l.parentTag='a href="#"'),i.mobileNav.attr("class",o+"_nav"),a=e('<div class="'+o+'_menu"></div>'),""!==l.brand){var r=e('<div class="'+o+'_brand">'+l.brand+"</div>");e(a).append(r)}i.btn=e(["<"+l.parentTag+' aria-haspopup="true" tabindex="0" class="'+o+"_btn "+o+'_collapsed">','<span class="'+o+'_menutxt">'+l.label+"</span>",'<span class="'+t+'">','<span class="'+o+'_icon-bar"></span>','<span class="'+o+'_icon-bar"></span>','<span class="'+o+'_icon-bar"></span>',"</span>","</"+l.parentTag+">"].join("")),e(a).append(i.btn),""!==l.appendTo?e(l.appendTo).append(a):e(l.prependTo).prepend(a),a.append(i.mobileNav);var d=i.mobileNav.find("li");e(d).each(function(){var n=e(this),t={};if(t.children=n.children("ul").attr("role","menu"),n.data("menu",t),t.children.length>0){var a=n.contents(),s=!1,r=[];e(a).each(function(){return e(this).is("ul")?!1:(r.push(this),void(e(this).is("a")&&(s=!0)))});var d=e("<"+l.parentTag+' role="menuitem" aria-haspopup="true" tabindex="-1" class="'+o+'_item"/>');if(l.allowParentLinks&&!l.nestedParentLinks&&s)e(r).wrapAll('<span class="'+o+"_parent-link "+o+'_row"/>').parent();else{var c=e(r).wrapAll(d).parent();c.addClass(o+"_row")}l.showChildren?n.addClass(o+"_open"):n.addClass(o+"_collapsed"),n.addClass(o+"_parent");var p=e('<span class="'+o+'_arrow">'+(l.showChildren?l.openedSymbol:l.closedSymbol)+"</span>");l.allowParentLinks&&!l.nestedParentLinks&&s&&(p=p.wrap(d).parent()),e(r).last().after(p)}else 0===n.children().length&&n.addClass(o+"_txtnode");n.children("a").attr("role","menuitem").click(function(n){l.closeOnClick&&!e(n.target).parent().closest("li").hasClass(o+"_parent")&&e(i.btn).click()}),l.closeOnClick&&l.allowParentLinks&&(n.children("a").children("a").click(function(n){e(i.btn).click()}),n.find("."+o+"_parent-link a:not(."+o+"_item)").click(function(n){e(i.btn).click()}))}),e(d).each(function(){var n=e(this).data("menu");l.showChildren||i._visibilityToggle(n.children,null,!1,null,!0)}),i._visibilityToggle(i.mobileNav,null,!1,"init",!0),i.mobileNav.attr("role","menu"),e(n).mousedown(function(){i._outlines(!1)}),e(n).keyup(function(){i._outlines(!0)}),e(i.btn).click(function(e){e.preventDefault(),i._menuToggle()}),i.mobileNav.on("click","."+o+"_item",function(n){n.preventDefault(),i._itemClick(e(this))}),e(i.btn).keydown(function(e){var n=e||event;13==n.keyCode&&(e.preventDefault(),i._menuToggle())}),i.mobileNav.on("keydown","."+o+"_item",function(n){var t=n||event;13==t.keyCode&&(n.preventDefault(),i._itemClick(e(n.target)))}),l.allowParentLinks&&l.nestedParentLinks&&e("."+o+"_item a").click(function(e){e.stopImmediatePropagation()})},a.prototype._menuToggle=function(e){var n=this,t=n.btn,a=n.mobileNav;t.hasClass(o+"_collapsed")?(t.removeClass(o+"_collapsed"),t.addClass(o+"_open")):(t.removeClass(o+"_open"),t.addClass(o+"_collapsed")),t.addClass(o+"_animating"),n._visibilityToggle(a,t.parent(),!0,t)},a.prototype._itemClick=function(e){var n=this,t=n.settings,a=e.data("menu");a||(a={},a.arrow=e.children("."+o+"_arrow"),a.ul=e.next("ul"),a.parent=e.parent(),a.parent.hasClass(o+"_parent-link")&&(a.parent=e.parent().parent(),a.ul=e.parent().next("ul")),e.data("menu",a)),a.parent.hasClass(o+"_collapsed")?(a.arrow.html(t.openedSymbol),a.parent.removeClass(o+"_collapsed"),a.parent.addClass(o+"_open"),a.parent.addClass(o+"_animating"),n._visibilityToggle(a.ul,a.parent,!0,e)):(a.arrow.html(t.closedSymbol),a.parent.addClass(o+"_collapsed"),a.parent.removeClass(o+"_open"),a.parent.addClass(o+"_animating"),n._visibilityToggle(a.ul,a.parent,!0,e))},a.prototype._visibilityToggle=function(n,t,a,i,s){function l(n,t){e(n).removeClass(o+"_animating"),e(t).removeClass(o+"_animating"),s||c.afterOpen(n)}function r(t,a){n.attr("aria-hidden","true"),p.attr("tabindex","-1"),d._setVisAttr(n,!0),n.hide(),e(t).removeClass(o+"_animating"),e(a).removeClass(o+"_animating"),s?"init"==t&&c.init():c.afterClose(t)}var d=this,c=d.settings,p=d._getActionItems(n),u=0;a&&(u=c.duration),n.hasClass(o+"_hidden")?(n.removeClass(o+"_hidden"),s||c.beforeOpen(i),"jquery"===c.animations?n.stop(!0,!0).slideDown(u,c.easingOpen,function(){l(i,t)}):"velocity"===c.animations&&n.velocity("finish").velocity("slideDown",{duration:c.duration,easing:c.easingOpen,complete:function(){l(i,t)}}),n.attr("aria-hidden","false"),p.attr("tabindex","0"),d._setVisAttr(n,!1)):(n.addClass(o+"_hidden"),s||c.beforeClose(i),"jquery"===c.animations?n.stop(!0,!0).slideUp(u,this.settings.easingClose,function(){r(i,t)}):"velocity"===c.animations&&n.velocity("finish").velocity("slideUp",{duration:c.duration,easing:c.easingClose,complete:function(){r(i,t)}}))},a.prototype._setVisAttr=function(n,t){var a=this,i=n.children("li").children("ul").not("."+o+"_hidden");t?i.each(function(){var n=e(this);n.attr("aria-hidden","true");var i=a._getActionItems(n);i.attr("tabindex","-1"),a._setVisAttr(n,t)}):i.each(function(){var n=e(this);n.attr("aria-hidden","false");var i=a._getActionItems(n);i.attr("tabindex","0"),a._setVisAttr(n,t)})},a.prototype._getActionItems=function(e){var n=e.data("menu");if(!n){n={};var t=e.children("li"),a=t.find("a");n.links=a.add(t.find("."+o+"_item")),e.data("menu",n)}return n.links},a.prototype._outlines=function(n){n?e("."+o+"_item, ."+o+"_btn").css("outline",""):e("."+o+"_item, ."+o+"_btn").css("outline","none")},a.prototype.toggle=function(){var e=this;e._menuToggle()},a.prototype.open=function(){var e=this;e.btn.hasClass(o+"_collapsed")&&e._menuToggle()},a.prototype.close=function(){var e=this;e.btn.hasClass(o+"_open")&&e._menuToggle()},e.fn[s]=function(n){var t=arguments;if(void 0===n||"object"==typeof n)return this.each(function(){e.data(this,"plugin_"+s)||e.data(this,"plugin_"+s,new a(this,n))});if("string"==typeof n&&"_"!==n[0]&&"init"!==n){var i;return this.each(function(){var o=e.data(this,"plugin_"+s);o instanceof a&&"function"==typeof o[n]&&(i=o[n].apply(o,Array.prototype.slice.call(t,1)))}),void 0!==i?i:this}}}(jQuery,document,window);