/*!
 * Dropdownhover v1.0.0 (http://bs-dropdownhover.kybarg.com)
 */
+function(o){"use strict";function t(t){return this.each(function(){var e=o(this),r=e.data("bs.dropdownhover"),i=e.data();void 0!==e.data("animations")&&null!==e.data("animations")&&(i.animations=o.isArray(i.animations)?i.animations:i.animations.split(" "));var s=o.extend({},n.DEFAULTS,i,"object"==typeof t&&t);r||e.data("bs.dropdownhover",r=new n(this,s))})}var n=function(t,n){this.options=n,this.$element=o(t);var e=this;this.dropdowns=this.$element.hasClass("dropdown-toggle")?this.$element.parent().find(".dropdown-menu").parent(".dropdown"):this.$element.find(".dropdown"),this.dropdowns.each(function(){o(this).on("mouseenter.bs.dropdownhover",function(t){e.show(o(this).children("a, button"))})}),this.dropdowns.each(function(){o(this).on("mouseleave.bs.dropdownhover",function(t){e.hide(o(this).children("a, button"))})})};n.TRANSITION_DURATION=300,n.DELAY=150,n.TIMEOUT,n.DEFAULTS={animations:["fadeInDown","fadeInRight","fadeInUp","fadeInLeft"]},n.prototype.show=function(t){var e=o(t);window.clearTimeout(n.TIMEOUT),o(".dropdown").not(e.parents()).each(function(){o(this).removeClass("open")});var r=this.options.animations[0];if(!e.is(".disabled, :disabled")){var i=e.parent(),s=i.hasClass("open");if(!s){var d=e.next(".dropdown-menu");i.addClass("open");var a=this.position(d);r="top"==a?this.options.animations[2]:"right"==a?this.options.animations[3]:"left"==a?this.options.animations[1]:this.options.animations[0],d.addClass("animated "+r);var h=o.support.transition&&d.hasClass("animated");h?d.one("bsTransitionEnd",function(){d.removeClass("animated "+r)}).emulateTransitionEnd(n.TRANSITION_DURATION):d.removeClass("animated "+r)}return!1}},n.prototype.hide=function(t){var e=o(t),r=e.parent();n.TIMEOUT=window.setTimeout(function(){r.removeClass("open")},n.DELAY)},n.prototype.position=function(t){var n=o(window);t.css({bottom:"",left:"",top:"",right:""}).removeClass("dropdownhover-top");var e={top:n.scrollTop(),left:n.scrollLeft()};e.right=e.left+n.width(),e.bottom=e.top+n.height();var r=t.offset();r.right=r.left+t.outerWidth(),r.bottom=r.top+t.outerHeight();var i=t.position();i.right=r.left+t.outerWidth(),i.bottom=r.top+t.outerHeight();var s="",d=t.parents(".dropdown-menu").length;if(d)i.left<0?(s="left",t.removeClass("dropdownhover-right").addClass("dropdownhover-left")):(s="right",t.addClass("dropdownhover-right").removeClass("dropdownhover-left")),r.left<e.left?(s="right",t.css({left:"100%",right:"auto"}).addClass("dropdownhover-right").removeClass("dropdownhover-left")):r.right>e.right&&(s="left",t.css({left:"auto",right:"100%"}).removeClass("dropdownhover-right").addClass("dropdownhover-left")),r.bottom>e.bottom?t.css({bottom:"auto",top:-(r.bottom-e.bottom)}):r.top<e.top&&t.css({bottom:-(e.top-r.top),top:"auto"});else{var a=t.parent(".dropdown"),h=a.offset();h.right=h.left+a.outerWidth(),h.bottom=h.top+a.outerHeight(),r.right>e.right&&t.css({left:-(r.right-e.right),right:"auto"}),r.bottom>e.bottom&&h.top-e.top>e.bottom-h.bottom||t.position().top<0?(s="top",t.css({bottom:"100%",top:"auto"}).addClass("dropdownhover-top").removeClass("dropdownhover-bottom")):(s="bottom",t.addClass("dropdownhover-bottom"))}return s};var e=o.fn.dropdownhover;o.fn.dropdownhover=t,o.fn.dropdownhover.Constructor=n,o.fn.dropdownhover.noConflict=function(){return o.fn.dropdownhover=e,this};var r;o(document).ready(function(){o(window).width()>=768&&o('[data-hover="dropdown"]').each(function(){var n=o(this);t.call(n,n.data())})}),o(window).on("resize",function(){clearTimeout(r),r=setTimeout(function(){o(window).width()>=768?o('[data-hover="dropdown"]').each(function(){var n=o(this);t.call(n,n.data())}):o('[data-hover="dropdown"]').each(function(){o(this).removeData("bs.dropdownhover"),o(this).hasClass("dropdown-toggle")?o(this).parent(".dropdown").find(".dropdown").andSelf().off("mouseenter.bs.dropdownhover mouseleave.bs.dropdownhover"):o(this).find(".dropdown").off("mouseenter.bs.dropdownhover mouseleave.bs.dropdownhover")})},200)})}(jQuery);
