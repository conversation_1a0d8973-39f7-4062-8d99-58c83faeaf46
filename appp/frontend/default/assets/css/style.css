@import url('https://fonts.googleapis.com/css?family=Raleway:300,400,700|Roboto:300,400,700');
html,
body {
    height: 100%;
}
img {
    max-width: 100%;
    height: auto
}
p {
    margin: 0 0 15px;
}
p:last-child {
    margin: 0;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0 0 25px;
    font-family: 'Raleway', sans-serif;
    font-weight: 700;
}
a {
    color: #444;
}
a:hover {
    color: #86bc42;
    text-decoration: none;
}
a,
a:active,
a:hover,
a:focus {
    outline: 0;
    text-decoration: none;
    -webkit-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
}
input,
textarea {
    outline: none;
}
textarea {
    resize: vertical;
}
ul {
    list-style: none;
    margin: 0;
    padding: 0;
}
body {
    font-family: 'Roboto', sans-serif;
    font-weight: 300;
    line-height: 26px;
    color: #86878b;
}
.browserupgrade {
    margin: 0.2em 0;
    background: #ccc;
    color: #000;
    padding: 0.2em 0;
}

.primary-bg {
    background: #86bc42;
}
.gray-bg {
    background: #f7f7f7;
}
.white-bg {
    background: #fff;
}
.vertical--middle {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
}
.vertical--middle div {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
}
.area-padding {
    padding: 70px 0;
}
.area-padding-btm {
    padding-bottom: 90px;
}
.mb-0 {
    margin-bottom: 0px !important;
}


.header-top-area {
    background-color: #222;
    min-height: 55px;
    line-height: 55px;
    position: relative;
    z-index: 1;
}

.header-top-area span {
    font-weight: 400;
}
.header-top-area ul {
    position: relative;
}


.header-top-area i,
.header-top-area a:hover,
.nav > li.active > a,
.logo h1 a span,
.address-right a:hover {
    color: #86bc42;
}
.header-top-area,
.header-top-area a {
    color: #fff;
}
.header-top-area i,
.header-top-area span {
    margin-right: 5px;
}
.header-top-area li,
#navigation li {
    display: inline-block;
}
.header-top-area li a i {
    color: #fff;
    -webkit-transition: all .3s ease 0s;
    transition: all .3s ease 0s;
}

.header-top-area li a:hover i {
    color: #86bc42;
}
.header-menu-area {
    background-color: #fff;
    -webkit-transition: all .5s ease 0s;
    transition: all .5s ease 0s;
}
.navbar-brand {
    margin-left: 0px !important;
    display: inline-block;
    font-size: 18px;
    color: #222;
    border: 2px solid #222;
    border-radius: 1px;
    padding: 10px 20px;
    position: relative;
    text-transform: uppercase;
    line-height: 28px;
    margin-top: 15px;
    font-weight: 700;
}

.navbar-brand:hover {
    color: #222;
}

.navbar-brand span {
    color: #86bc42;
}
.main-menu {
    margin: 0;
    padding: 0;
    text-align: right;
  }
  
  .main-menu li {
    display: inline-block;
    position: relative;
    padding: 25px 0px;
  }
  
  .main-menu li a {
    display: inline-block;
    color: #222;
    padding: 0px 20px;
  }
  
  .main-menu li a.active {
    background-color: #86bc42;
  }
  
  .main-menu li a.active:hover {
    color: #86bc42;
    opacity: .9;
  }
  
  .main-menu li > .sub-menu {
    margin: 0;
    padding: 15px 0px;
    list-style: none;
    position: absolute;
    left: 0;
    top: 100%;
    width: 210px;
    background-color: #fff;
    opacity: 0;
    visibility: hidden;
    transition: all .4s ease-out;
    z-index: 333;
  }
  
  .main-menu li > .sub-menu li {
    display: block;
    padding: 0px 0;
  }
  
  .main-menu li > .sub-menu li a {
    display: block;
    color: #adb5bd;
    padding: 5px 20px;
    text-align: left;
  }
  
  .main-menu li > .sub-menu li:hover > a {
    color: #86bc42;
  }
  .main-menu li > .sub-menu li > .sub-menu {
    left: 100%;
    top: 0;
  }

  .main-menu li.dropdown-left > .sub-menu {
      left: auto;
      right: 0;
  }
  .main-menu li.dropdown-left > .sub-menu a {
      text-align: right;
  }
  .main-menu li.dropdown-left > .sub-menu a i {
      position: absolute;
      left: 20px;
      top: 50%;
      transform: translateY(-50%)
  }
  .main-menu li.dropdown-left > .sub-menu li > .sub-menu {
    left: auto;
    right: 100%;
}

  .main-menu li:hover > a {
    color: #86bc42;
  }
  
  .main-menu li:hover > .sub-menu {
    visibility: visible;
    opacity: 1;
  }

  .slicknav_btn {
    border: 1px solid;
    border-color: #3498db;
    background: transparent;
    margin-bottom: 15px;
  }
  
  .slicknav_menu {
    background: transparent;
  }
  
  .slicknav_menu .slicknav_icon-bar {
    background: #3498db;
  }
  
  .slicknav_nav {
    background-color: #fff;
    color: #3498db;
    padding: 10px;
    text-align: left;
  }
  
  .slicknav_nav a {
    color: #3498db;
  }
  .slicknav_nav a:hover {
    background: #3498db;
    color: #fff;
    border-radius: 0px;
    outline: none;
  }
  
  .slicknav_nav .slicknav_arrow {
    width: 35px;
    background: #3498db;
    color: #fff;
    float: right;
    text-align: center;
    font-size: 18px;
  }
  
  .slicknav_nav .slicknav_row:hover {
    background: transparent;
  }
  
  .navbar-toggler {
    display: none;
  }
  
  .mobile-menu {
    display: none;
  }
  
  .slicknav_menu {
    display: none;
  }
  
  @media (max-width: 991.98px) {
    .navbar-brand {
      position: absolute;
      top: 10px;
      left: 7px;
    }
    .navbar-brand img {
      width: 75%;
    }
    .slicknav_menu {
      display: block;
    }
    .main-menu {
      display: none;
    }
    .slicknav_btn {
        margin-top: 15px;
      margin-bottom: 25px;
    }
    .slicknav_nav a .fa {
        display: none;
      }
  }
  
  @media (max-width: 767.98px) {
    .navbarheader {
      width: 100%;
    }
    .navbar-brand {
      position: absolute;
      margin-top: 0;
    }
    .navbar-brand img {
      width: 60%;
    }
    .slicknav_btn {
      margin-bottom: 25px;
    }
    .slicknav_menu {
      display: block;
    }
    .slicknav_nav .active a {
      background: #e91e63;
      color: #fff;
    }
    .slicknav_nav a:hover {
      background: #3498db;
      color: #fff;
      border-radius: 0px;
      outline: none;
    }
    .slicknav_nav a .fa {
      display: none;
    }
    .slicknav_nav .slicknav_arrow {
      background: #3498db;
      color: #fff;
    }
    .slicknav_nav .dropdown li a.active {
      background: #f8f9fa;
      color: #3498db;
    }
    .slicknav_nav .dropdown li a :focus {
      outline: 0;
    }
  }

/*===================================
    slider area start
=====================================*/
.slider-area {
    position: relative;
    overflow: hidden;
}
.slider-area .single-slide {
    position: relative;
}
.banner-overlay {
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 100%;
    background: #000;
    opacity: .8;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
}
.p-relative {
    position: relative;
}
.caption {
    left: 50%;
    position: absolute;
    top: 50%;
    -webkit-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
}

.caption h2 {
    font-size: 56px;
    color: #fff;
    font-style: italic;
    text-transform: uppercase;
}
.caption h2 span {
    color: #86bc42;
    display: block;
}
.caption p {
    font-size: 18px;
    color: #fff;
}
.btn-1 {
    display: inline-block;
    font-size: 14px;
    position: relative;
    padding: 0 28px;
    height: 58px;
    line-height: 58px;
    border-radius: 5px;
    background: #86bc42;
    color: #fff;
    text-transform: uppercase;
    z-index: 11;
    -webkit-box-shadow: 0px 1px 5px 0px rgba(50, 50, 50, 0.67);
}
.btn-1:hover {
    color: #fff;
}
.btn-1:before {
    content: '';
    border-radius: 5px;
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: 50%;
    z-index: -1;
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    -webkit-transition: all 0.5s ease-in-out;
    transition:all 0.5s ease-in-out;
}
.btn-1:hover:before {
    width: 100%;
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    background: rgba(0,0,0,0.3);
}
.btn-1 i {
    margin-left: 5px;
}
.slider-area.owl-carousel .owl-nav div {
    position: absolute;
    left: 0;
    top: 50%;
    -webkit-transform: translateY(-50%);
            transform: translateY(-50%);
    background: #fff;
    padding: 10px 15px;
    text-align: center;
    font-size: 20px;
    -webkit-transition: all .4s ease 0s;
    transition: all .4s ease 0s;
}
.slider-area.owl-carousel .owl-nav div:hover {
    background: #86bc42;
    color: #fff;
}
.slider-area.owl-carousel .owl-nav div.owl-next {
    left: inherit;
    right: 0;
}

/*===================================
    section title area start
=====================================*/
.section-title {
    position: relative;
    padding-bottom: 15px;
    margin-bottom: 50px;
}
.section-title h2 {
    margin: 0;
    font-size: 35px;
    color: #212121;
}
.section-title .star {
    height: 10px;
    width: 10px;
    background-color: #86bc42;
    display: inline-block;
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
}

.section-title.white-title h2 {
    color: #fff;
}
.section-title.white-title .star {
    background-color: #fff;
}
.section-title.bradcam-title {
    margin-bottom: 0px;
}



/*===================================
    about area start
=====================================*/
.about-content {margin-top: 80px;}

.primary-btn {
    display: inline-block;
    text-transform: uppercase;
    position: relative;
    background: #86bc42;
    color: #fff;
    padding: 15px 0;
    width: 145px;
    text-align: center;
    border-radius: 0px;
    font-size: 16px;
    font-weight: 400;
    margin-top: 15px;
    position: relative;
    overflow: hidden;
    z-index: 1;
 }

.primary-btn:after {
    content: "";
    position: absolute;
    left: -42px;
    top: -5px;
    width: 110%;
    height: 100%;
    background: #000;
    -webkit-transform: rotate(-45deg);
            transform: rotate(-45deg);
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    -webkit-transition: all .6s ease 0s;
    transition: all .6s ease 0s;
    -webkit-transform-origin: 0 0;
            transform-origin: 0 0;
    z-index: -1;
}
.primary-btn:hover:after {
    left: 0;
    top: 0;
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}
.content-img {
    position: relative;
    z-index: 1;
    padding: 0 20px;
    margin-right: -17px;
}
.content-img:after {
    content: "";
    position: absolute;
    left: 0;
    top: 70px;
    width: 100%;
    height: 85%;
    border: 10px solid #86bc42;
    z-index: -1;
}

/*===================================
    funfact area start
=====================================*/
.funfact-area, .testimonial-area {
    min-height: 450px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    position: relative;
    z-index: 1;
}
.funfact-area:after, .testimonial-area:after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #000;
    opacity: .8;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
    z-index: -1;
}
.funfact-area {
    background: url("../img/funfact-bg.jpg") no-repeat fixed center center / cover;
}
.single-counter {
    position: relative;
    z-index: 999;
}
.counter-icon i {
    border: 2px solid #86bc42;
    border-radius: 50%;
    color: #86bc42;
    font-size: 23px;
    height: 65px;
    line-height: 63px;
    text-align: center;
    width: 65px;
}
.counter-text {
    margin-top: 16px;
}
.counter-text h2 {
    color: #fff;
    font-size: 40px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
}
.counter-text p {
    color: #fff;
    font-size: 16px;
    font-weight: 500;
    margin-top: 8px;
    text-transform: uppercase;
}

#particles-js canvas {
    bottom: 0;
    left: 0;
    margin: auto;
    position: absolute;
    right: 0;
    top: 0;
    z-index: 1;
}

/*===================================
    teacher list area start
=====================================*/
.our-teacher-area {
    border-top: 1px solid #e1e1e1;
    border-bottom: 1px solid #e1e1e1;
}
.teacher-list {
    position: relative;
    margin-bottom: 30px;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.teacher-list {
    position: relative;
    margin-bottom: 30px;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.1);
    overflow: hidden;
}
.teacher-list .teacher__thumb {
    position: relative;
    overflow: hidden;
}
.teacher-list .teacher__social {
    background-color: #86bc42;
    position: absolute;
    bottom: -10%;
    width:  100%;
    height: 60px;
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    flex-flow: row wrap;
    opacity: 0;
    transition: all .4s ease 0s;
}
.teacher-list:hover .teacher__social {
    bottom: 0;
    opacity: 1;
}
.teacher-list .teacher__social a {
    display: inline-flex;
    border: 1px solid #fff;
    color: #fff;
    width: 30px;
    height: 30px;
    justify-content: center;
    align-items: center;
    flex-flow: row wrap;
    transition: all .4s ease 0s;
}
.teacher-list .teacher__social a:hover {
    background-color: #fff;
    color: #86bc42;
}

.teacher-list .teacher__body {
    padding-top: 30px; 
    padding-bottom: 30px;
}
.teacher-list .teacher__body h2 {
    color: #444;
    font-size: 24px;
    line-height: 24px;
    margin: 0px 0px 10px 0px;
}
.teacher-list .teacher__body h2 span {
    display: block;
    font-size: 70%;
    font-weight: 400;
    margin-top: 5px;
}

/*===================================
    event schedule area start
=====================================*/
.single-event-list {
    position: relative;
    border: 1px solid #ddd;
    margin-bottom: 30px;
    -webkit-transition: all .4s ease 0s;
    transition: all .4s ease 0s;
}
.single-event-list:hover {
    box-shadow: 2px 0px 20px 4px rgba(0, 0, 0, 0.2);
}
.event-date {
    position: absolute;
    left: 0px;
    top: 20px;
    color: #fff;
    width: 70px;
    height: 85px;
    z-index: 1;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    font-size: 16px;
    font-weight: 700;
}

.event-date:after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #86bc42;
    opacity: .8;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
    z-index: -1;
}
.event-date.second-date {
    left: inherit;
    right: 0;
}
.event-carousel .owl-stage-outer {
    padding: 10px 0;
}

.event-content {
    padding: 20px 30px;
}
.event-info {
    margin-bottom: 15px;
}
.event-info h4 {
    margin-bottom: 15px;
    text-transform: uppercase;
}
.event-title {
    font-weight: 700;
    color: #333;
    margin-right: 5px;
}

.primary-btn.style--two:after {
    bottom: -5px;
    -webkit-transform: rotate(45deg);
            transform: rotate(45deg);
}

.primary-btn.style--two:hover:after {
    bottom: 0;
    -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
}


/*===================================
    gallery area start
=====================================*/
.gallery-item {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -ms-flex-wrap: wrap;
        flex-wrap: wrap;
    margin-bottom: 30px;
}

.single-gallery {
    width: calc(100% / 4);
    position: relative;
    overflow: hidden;
}
.single-gallery:after {
    content: "";
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: #86bc42;
    z-index: 11;
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    -webkit-transition: all .5s ease 0s;
    transition: all .5s ease 0s;
}
.single-gallery:hover:after {
    opacity: .8;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
}
.single-gallery a {
    display: block;
}
.single-gallery img {
    width: 100%;
    height: 100%;
}
.overlay {
    position: absolute;
    left: 50%;
    top: 50%;
    -webkit-transform: translate(-50%,-50%) scale(4.5);
            transform: translate(-50%,-50%) scale(4.5);
    z-index: 22;
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    -webkit-transition: all .5s ease 0s;
    transition: all .5s ease 0s;
}

.overlay a {
    position: relative;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    text-align: center;
    line-height: 40px;
    color: #86bc42;
    -webkit-transition: all .5s ease 0s;
    transition: all .5s ease 0s;
    background-color: #fff;
}

.overlay a:after {
    border-radius: 50%;
    bottom: -4px;
    content: "";
    left: -4px;
    opacity: 0;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";
    position: absolute;
    right: -4px;
    top: -4px;
    -webkit-transform: scale(0.8);
            transform: scale(0.8);
    box-shadow: 0 0 0 3px #fff;
    -webkit-transition: all 0.3s cubic-bezier(0.68, -0.07, 0.25, 0.93) 0s;
    transition: all 0.3s cubic-bezier(0.68, -0.07, 0.25, 0.93) 0s;
}
.overlay a:hover:after {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    -webkit-transform: scale(1);
            transform: scale(1);
}

.single-gallery:hover .overlay {
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
    -webkit-transform: translate(-50%,-50%) scale(1.2);
            transform: translate(-50%,-50%) scale(1.2);
}

/*===================================
    notice area start
=====================================*/
.notice-area {
    border-top: 1px solid #eee;
}
.single-notice {
    text-align: center;
    min-height: 300px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-orient: vertical;
    -webkit-box-direction: normal;
        -ms-flex-direction: column;
            flex-direction: column;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    padding: 0 20px;
    margin-bottom: 30px;
    border-radius: 2px;
    -webkit-transition: all .5s ease-in-out 0s;
    transition: all .5s ease-in-out 0s;
    border: 1px solid #ddd;
    background: #fff;
}
.single-notice:hover {
    box-shadow: 0 0 25px 3px rgba(0,0,0,.2);
    background: #fff;
}
.notice-content h3 {
    margin-bottom: 15px;
}
.notice-meta {
    margin-bottom: 10px;
}
.notice-meta span {
    margin-right: 10px;
}
.notice-meta span i {
    margin-right: 5px;
}
.notice-meta span a {
    color: #86878b;
}
.notice-meta span a:hover {
    color: #86bc42;
}

.notice-content .read-more-btn {
    display: inline-block;
    text-transform: capitalize;
    font-size: 16px;
    font-weight: 300;
    color: #86878b;
}
.notice-content .read-more-btn:hover {color: #86bc42;}
.notice-content .read-more-btn i {
    -webkit-transition: margin-left .4s ease 0s;
    transition: margin-left .4s ease 0s;
}
.notice-content .read-more-btn:hover i{
    margin-left: 5px;
}

.notice-carousel.owl-carousel .owl-stage-outer {
    padding: 15px 0;
}
.notice-carousel.owl-carousel .owl-nav div {
    position: absolute;
    left: 49%;
    bottom: -30px;
    border: 1px solid #e1e1e1;
    width: 55px;
    height: 30px;
    text-align: center;
    line-height: 30px;
    font-size: 20px;
    -webkit-transform: translateX(-50%);
            transform: translateX(-50%);
    -webkit-transition: all .4s ease 0s;
    transition: all .4s ease 0s;
}
.notice-carousel.owl-carousel .owl-nav div:hover {
    border-color: #86bc42;
    background: #86bc42;
    color: #fff;
    opacity: 1;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=100)";
}
.notice-carousel.owl-carousel .owl-nav div.owl-next {
    left: 54%;
}

/*===================================
    contact form area start
=====================================*/
.our-contact-us-area .container.white-bg {
    padding-top: 50px;
    padding-bottom: 40px;
    box-shadow: 0px 0px 18px 2px rgba(0, 0, 0, .2);
}
.company-info {
    border: 1px solid #86878b;
    border-radius: 5px;
    margin-bottom: 15px;
    padding: 20px;
    -webkit-transition: all .4s ease 0s;
    transition: all .4s ease 0s;
    text-align: center;
}
.company-info:hover {
    background: #86bc42;
    color: #fff;
    border-color: #86bc42;
}
.info-icon i {
    border: 1px solid #86878b;
    border-radius: 5px;
    font-size: 25px;
    height: 45px;
    line-height: 43px;
    text-align: center;
    width: 45px;
    -webkit-transition: .5s;
    transition: .5s;
}
.company-info:hover .info-icon i {
    border-color: #fff;
    color: #fff;
}
.info-text {
    margin-top: 10px;
}

.input-box:nth-of-type(-n+2) {
    width: 49%;
    margin-right: 2%;
}
.input-box:nth-of-type(2) {
    margin-right: 0;
}
.input-box {
    width: 100%;
    margin-bottom: 20px;
    float: left;
    text-align: right;
}
.input-box input, .input-box textarea {
    background: transparent;
    border: 1px solid #86878b;
    border-radius: 5px;
    height: 54px;
    line-height: 25px;
    margin: 0 0 10px;
    outline: medium none;
    padding-left: 15px;
    -webkit-transition: all 0.3s ease 0s;
    transition: all 0.3s ease 0s;
    width: 100%;
}
.input-box input:focus, .input-box textarea:focus {
    border-color: #86bc42;
}
.input-box textarea {
    height: 188px;
    padding-top: 13px;
    margin: 0;
}
.input-box h5 {
    text-transform: capitalize;
    font-weight: 300;
    margin: 0;
}
.send-btn {
    border: 2px solid #86878b;
    border-radius: 30px;
    display: block;
    font-size: 16px;
    font-weight: 500;
    padding: 12px 30px 12px;
    -webkit-transition: all 0.5s ease 0s;
    transition: all 0.5s ease 0s;
    background: transparent;
    line-height: 1;
    text-transform: uppercase;
}
button.send-btn:hover {
    background: #86bc42;
    border-color: #86bc42;
    color: #fff;
}

/*===================================
    footer area start
=====================================*/
.footer-top-area {
    background: #333;
}
.footer-widget {
    color:#fff;
}
.footer-widget .logo.footer-logo {
    margin: 0 0 25px;
}

.footer-widget .logo.footer-logo h1 {
    border-color: #fff;
}

.footer-widget .logo.footer-logo a {color: #fff;}
.footer-widget .footer-social{
    margin-top: 25px;
}
.footer-widget .footer-social li {
    display:-webkit-inline-box;
    display:-ms-inline-flexbox;
    display:inline-flex;
}
.footer-widget .footer-social li a {
    color:#fff;
    width: 35px;
    height: 35px;
    text-align: center;
    line-height: 35px;
    border-radius: 50%;
    background: #86bc42;
    margin-right: 5px;
    -webkit-transition: all .4s ease 0s;
    transition: all .4s ease 0s;
}
.footer-widget .footer-social li a:hover {
    border-radius: 3px;
}

.footer-widget h2 {
    position: relative;
    padding-bottom: 10px;
    text-transform: uppercase;
    font-size: 24px;
}

.footer-widget h2:after, .footer-widget h2:before {
    content: "";
    position: absolute;
    left: 0;
    bottom: 0;
    width: 100%;
    height: 3px;
}
.footer-widget h2:before {
    background: #86bc42;
}

.footer-widget h2:after {
    background: #222;
    width: 30%;
}

.footer-widget > ul > li {
    display: block;
    border-bottom: 1px dashed #ddd;
}
.footer-widget > ul > li > a {
    display: block;
    color: #fff;
    text-transform: uppercase;
    font-weight: 400;
    padding: 5px 0;
    -webkit-transition: all .6 ease 0s;
    transition: all .6 ease 0s;
    position: relative;
    z-index: 1;
}

.footer-widget > ul > li > a:hover {
    margin-left: 10px;
    color: #86bc42;
}

.footer-area {
    background-color: #222;
    color: #fff;
    padding: 30px 0;
    font-size: 16px;
}
.copyright {
    margin-top: 12px;
}
.copyright a {
    color: #fff;
    font-weight: 400;
    -webkit-transition: all .4s ease 0s;
    transition: all .4s ease 0s;
}
.copyright a:hover {
    color: #86bc42
}
.footer-social-icons ul li {
    display: -webkit-inline-box;
    display: -ms-inline-flexbox;
    display: inline-flex;
}
.footer-social-icons ul li a {
    border: 2px solid #fff;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
        -ms-flex-align: center;
            align-items: center;
    -webkit-box-pack: center;
        -ms-flex-pack: center;
            justify-content: center;
    color: #fff;
    margin-left: 5px;
    -webkit-transition: all .4s ease 0s;
    transition: all .4s ease 0s;
}
.footer-social-icons ul li a:hover {
    background: #86bc42;
    border-color: #86bc42;
}

/*===================================
        scroll start
=====================================*/
#scroll {
    background: #222;
    bottom: 10px;
    color: #fff;
    cursor: pointer;
    font-size: 25px;
    height: 40px;
    line-height: 40px;
    position: fixed;
    right: 15px;
    text-align: center;
    -webkit-transform: rotate(45deg);
    transform: rotate(45deg);
    -webkit-transition: all 0.4s linear 0s;
    transition: all 0.4s linear 0s;
    width: 40px;
    z-index: 99;
    border-radius: 2px;
}
#scroll:hover {
    background: #86bc42;
}
#scroll i {
    -webkit-transform: rotate(-45deg);
    transform: rotate(-45deg);
}




.bradcam-area {
    background: url("../img/bradcam.jpg") no-repeat fixed center center / cover;
    color:#fff;
    position: relative;
    z-index: 1;
}
.bradcam-area:before {
    background: #000;
    content: "";
    height: 100%;
    left: 0;
    position: absolute;
    top: 0;
    width: 100%;
    opacity: .8;
    -ms-filter: "progid:DXImageTransform.Microsoft.Alpha(Opacity=80)";
    z-index: -1;
}

.bradcam-wrap nav.bradcam-inner a {
    color: #fff;
    font-weight: 600;
}
.bradcam-wrap nav.bradcam-inner a:hover {
    color: #86bc42;
}
.bradcam-wrap nav.bradcam-inner .brd-separetor {
    display: inline-block;
    margin: 0 5px;
}
.bradcam-wrap nav.bradcam-inner span {
    font-size: 14px;
    font-weight: 600;
    line-height: 20px;
}













.slider-area .single-slide img {
    height: 645px;
    width: 1920px;
}

.single-teacher-list img {
    height: 300px;
    width: 100%;
}




.login-form {
    position: relative;
    background-color: #fff;
    box-shadow: 0px 2px 14px rgba(0,0,0,.1);
    padding-top: 40px;
    padding-bottom: 40px;
}

.login-title {
    position: relative;
    color: #fff;
    margin-bottom: 35px;
    height: 105px;
    z-index: 1;
}

.login-title h3 {
    background-color: #86bc42;
    display: inline-block;
    padding: 40px;
    margin: 0;
    position: absolute;
    left: -9px;
    top: 0;
}

.login-form form {
    padding-right: 40px;
    padding-left: 40px;
}

.login-title::before, .login-title::after {
    content: "";
    position: absolute;
    left: -9px;
}

.login-title::before {
    top: -10px;
    border-left: 10px solid transparent;
    border-bottom: 10px solid #649a21;
}
.login-title::after {
    bottom: -10%;
    border-left: 10px solid transparent;
    border-top: 10px solid #649a21;
}

.login-title h3::after, .login-title h3::before {
    content: "";
    position: absolute;
    right: 0;
    bottom: 0;
}

.login-title h3::after {
    border-right: 25px solid transparent;
    border-top: 25px solid #649a21;
}

.login-title h3::before {
    border-left: 25px solid transparent;
    border-bottom: 25px solid #fff;
}

.login-form .form-control {
    border: 2px solid #86878b;
    box-shadow: none;
}

.login-form .form-control:focus {
    border-color: #86bc42;
}



.single-event-details {
    position: relative;
}
.single-event-details img {
    width: 100%;
}
.single-event-details .event-info h4, .recen-notice-content h3 {
    color: #444;
}
.single-event-details .event-time, .single-event-details .event-venue {
    display: inline-block;
}

.single-event-details .event-time {
    margin-right: 10px;
}

.recent-event-title {
    color: #444;
}
.recent-events-list {
    overflow: hidden;
    margin-bottom: 5px
}

.recent-events-list > div {
    float: left;
}

.recent-events-list .event-img {
    width: 40%;
}

.recent-events-list .event-content {
    width: 60%;
    padding: 0 0 0 10px;
}

.recent-events-list .event-content .event-info h4 {
    font-size: 12px;
    margin-bottom: 5px;
}
.recent-events-list .event-content .event-info span {
    font-size: 11px;
    line-height: 14px;
}

.featured-slider img {
    width: 100%;
    height: 520px
}

.teacher__thumb img {
    max-width: 100%;
    height: 250px;
}

.event-img img {
    max-width: 100%;
    height: 250px;
}


.blog-section {
    width: 100%;
    height: auto;
    border: 1px solid #dfdfdf;
    overflow: hidden;
    padding: 15px;
    box-shadow: 0px 0px 0px 0px #dfdfdf;

}
.blog {
    margin: 0 0px 20px 0;
    float: left;
    width: 100%;
    padding: 0px 0 0px 0px;
    font-size: 15px;
}
.blog header {
    font-weight: 700 !important;
    margin: 3px 0 10px 0;
    min-height: 0px;
    font-size: 22px;
}

.blog h2 a, .blog h2 a:visited {
    color: #464646;
    text-decoration: none;
}

.blog .blog-body {
    margin: 10px 0 0;
}

.blog img {
    float: left;
    max-width: 100%;
    height: auto;
    margin: 0 25px 10px 0;
    display: inline;
    width: 150px;
    height: 120px;
}

.blog p {
    display: inline;
    font-size: 16px;
    color: #464646;
}

.blog p a {
    color: #E82C0C;
}

.blog-section hr {
    overflow: hidden;
    width: 100%;
}

.blog-recennt-post {
    width: 100%;
    height: auto;
    min-height: 450px;
    border: 1px solid #dfdfdf;
    overflow: hidden;
    padding: 15px;
    box-shadow: 0px 0px 0px 0px #dfdfdf;
}

.blog-recennt-post h2 {
    font-size: 22px;
    color: #464646;
    text-align: center;
    margin-bottom: 15px;
}
.blog-recennt-post h2 span {
    color: #E82C0C;
}

.blog-recennt-post .post-title {
    color: #464646;
    margin: 15px 0px;
}

.blog img.fixedsize {
    width: 70%;
    height: auto;
}

.recent-events-list .eventView-img {
    width: 40%;
}

.eventView-img img {
    width: 100%;
    height: 100%;
}

.recent-events-list .eventView-img img {
    height: 75px;
}