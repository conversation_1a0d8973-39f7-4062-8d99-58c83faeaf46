/*! ========================================================================
 * Bootstrap Toggle: bootstrap-toggle.css v2.2.0
 * http://www.bootstraptoggle.com
 * ========================================================================
 * Copyright 2014 Min Hur, The New York Times Company
 * Licensed under MIT
 * ======================================================================== */
.checkbox label .toggle,.checkbox-inline .toggle{margin-left:-20px;margin-right:5px}
.toggle{position:relative;overflow:hidden}
.toggle input[type=checkbox]{display:none}
.toggle-group{position:absolute;width:200%;top:0;bottom:0;left:0;transition:left .35s;-webkit-transition:left .35s;-moz-user-select:none;-webkit-user-select:none}
.toggle.off .toggle-group{left:-100%}
.toggle-on{position:absolute;top:0;bottom:0;left:0;right:50%;margin:0;border:0;border-radius:0}
.toggle-off{position:absolute;top:0;bottom:0;left:50%;right:0;margin:0;border:0;border-radius:0}
.toggle-handle{position:relative;margin:0 auto;padding-top:0;padding-bottom:0;height:100%;width:0;border-width:0 1px}
.toggle.btn{min-width:59px;min-height:34px}
.toggle-on.btn{padding-right:24px}
.toggle-off.btn{padding-left:24px}
.toggle.btn-lg{min-width:79px;min-height:45px}
.toggle-on.btn-lg{padding-right:31px}
.toggle-off.btn-lg{padding-left:31px}
.toggle-handle.btn-lg{width:40px}
.toggle.btn-sm{min-width:50px;min-height:30px}
.toggle-on.btn-sm{padding-right:20px}
.toggle-off.btn-sm{padding-left:20px}
.toggle.btn-xs{min-width:35px;min-height:22px}
.toggle-on.btn-xs{padding-right:12px}
.toggle-off.btn-xs{padding-left:12px}

.toggle.btn{height: 26px !important;
    min-width: 54px; border-radius: 50px; min-height: 30px; border: 2px solid #f6f7fa;}
.si .toggle.btn{      float: right;     position: relative;    right: 60px;}

.toggle-group .btn, .toggle-group .btn:hover{color: transparent;}

.btn .toggle-handle {
    border-radius: 100%;
    border-width: 0px;
    height: 22px;
    left: 26px;
    margin: 0 auto;
    padding: 0;
    position: absolute;
    top: 2px;
    width: 22px;
}

.btn.off .toggle-handle {
    border-radius: 100%;
    border-width: 0px;
    height: 22px;
    left: 52px;
    margin: 0 auto;
    padding: 0;
    position: absolute;
    top: 2px;
    width: 22px;
}



input[type="radio"], input[type="checkbox"] {
    display: none;
}
input[type="radio"] + label, input[type="checkbox"] + label { line-height: 39px; cursor: pointer; position: relative;}
input[type="radio"] + label span.fa-stack, input[type="checkbox"] + label span.fa-stack { float: left; margin: 0 20px 0 0}


input[type="radio"] + label .radio-button{width: 39px; height: 39px; border: 2px solid #e1e8f8; border-radius: 100%;}
input[type="radio"] + label .active{display: none;}
input[type="radio"] + label .active{width: 35px; height: 35px; line-height: 35px; font-size: 14px; border-radius: 100%;text-align: center;}
input[type="radio"] + label:hover .active{display: block; color: #efefef;}
input[type="radio"]:checked + label .active{display: block; color: #fff; background: #44a1ef;}

input[type="checkbox"] + label .checkbox-button{width: 39px; height: 39px; border: 2px solid #e1e8f8; border-radius: 3px;}
input[type="checkbox"] + label .active{display: none;}
input[type="checkbox"] + label .active{width: 35px; height: 35px; line-height: 35px; font-size: 14px; border-radius: 2px;text-align: center;}
input[type="checkbox"] + label:hover .active{display: block; color: #efefef;}
input[type="checkbox"]:checked + label .active{display: block; color: #fff; background: #44a1ef;}

