@font-face {
  font-family: 'iniicon';
  src:  url('ini-icon.eot?2rhnix');
  src:  url('ini-icon.eot?2rhnix#iefix') format('embedded-opentype'),
    url('ini-icon.ttf?2rhnix') format('truetype'),
    url('ini-icon.woff?2rhnix') format('woff'),
    url('ini-icon.svg?2rhnix#ini-icon') format('svg');
  font-weight: normal;
  font-style: normal;
}

[class^="iniicon-"], [class*=" iniicon-"] {
  /* use !important to prevent issues with browser extensions that change fonts */
  font-family: 'iniicon' !important;
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;

  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.iniicon-productsale:before {
  content: "\e900";
}
.iniicon-productpurchase:before {
  content: "\e901";
}
.iniicon-maininventory:before {
  content: "\e902";
}
.iniicon-attendanceoverviewreport:before {
  content: "\e903";
}
.iniicon-onlineexamreport:before {
  content: "\e904";
}
.iniicon-progresscardreport:before {
  content: "\e905";
}
.iniicon-studentfinereport:before {
  content: "\e906";
}
.iniicon-idcardreport:before {
  content: "\e907";
}
.iniicon-marksheetreport:before {
  content: "\e908";
}
.iniicon-meritstagereport:before {
  content: "\e909";
}
.iniicon-terminalreport:before {
  content: "\e90a";
}
.iniicon-tabulationsheetreport:before {
  content: "\e90b";
}
.iniicon-admitcardreport:before {
  content: "\e90c";
}
.iniicon-sociallink:before {
  content: "\e90d";
}
.iniicon-transactionreport:before {
  content: "\e90e";
}
.iniicon-duefeesreport:before {
  content: "\e90f";
}
.iniicon-feesreport:before {
  content: "\e910";
}
.iniicon-balancefeesreport:before {
  content: "\e911";
}
.iniicon-examschedulereport:before {
  content: "\e912";
}
.iniicon-routinereport:before {
  content: "\e913";
}
.iniicon-income:before {
  content: "\e914";
}
.iniicon-ebook:before {
  content: "\e915";
}
.iniicon-librarycardreport:before {
  content: "\e916";
}
.iniicon-leaveapplicationreport:before {
  content: "\e917";
}
.iniicon-librarybooksreport:before {
  content: "\e918";
}
.iniicon-librarybookissuereport:before {
  content: "\e919";
}
.iniicon-onlineexamquestionreport:before {
  content: "\e91a";
}
.iniicon-productsalereport:before {
  content: "\e91b";
}
.iniicon-productpurchasereport:before {
  content: "\e91c";
}
.iniicon-searchpaymentfeesreport:before {
  content: "\e91d";
}
.iniicon-salaryreport:before {
  content: "\e91e";
}
.iniicon-fmenu:before {
  content: "\e91f";
}
.iniicon-leavecategory:before {
  content: "\e920";
}
.iniicon-leaveassign:before {
  content: "\e921";
}
.iniicon-leaveapplication:before {
  content: "\e922";
}
.iniicon-leaveapply:before {
  content: "\e923";
}
.iniicon-mainleaveapplication:before {
  content: "\e924";
}
.iniicon-productcategory:before {
  content: "\e925";
}
.iniicon-productsupplier:before {
  content: "\e926";
}
.iniicon-productwarehouse:before {
  content: "\e927";
}
.iniicon-product:before {
  content: "\e928";
}
