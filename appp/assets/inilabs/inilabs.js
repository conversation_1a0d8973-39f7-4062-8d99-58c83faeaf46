// var url      = window.location.href;
// var res = url.split('/');
//
// var len = res.length;
// var arrlen = 0;
// var url_true = "";
// for (var i = len; i >= 2; i--) {
// 	var arrlen = i-1;
//     if(res[arrlen] == 'add' || res[arrlen] == 'edit' || res[arrlen] == 'view' || res[arrlen] == 'sent' || res[arrlen] == 'trash' || res[arrlen] == 'fav_message') { url_true =+ '/index'; } else { url_true =+ '/'+res[arrlen];
//     }
// }
// url = res[0]+'/'+url_true;
//
// // console.log(url_true);
//
// if($("a[href$='"+url+"']").parent().parent().attr('class') == 'treeview-menu') {
//     $("a[href$='"+url+"']").parent().parent().css('display', 'block');
//     $("a[href$='"+url+"']").parent().parent().parent().addClass('active');
//     $("a[href$='"+url+"']").parent().addClass('active');
// } else {
//     $("a[href$='"+url+"']").parent().addClass('active');
// }


        
