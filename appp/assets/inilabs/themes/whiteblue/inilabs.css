/*
| -----------------------------------------------------
| PRODUCT NAME:   INILABS SCHOOL MANAGEMENT SYSTEM
| -----------------------------------------------------
| AUTHOR:     INILABS TEAM
| -----------------------------------------------------
| EMAIL:      <EMAIL>
| -----------------------------------------------------
| COPYRIGHT:    RESERVED BY INILABS IT
| -----------------------------------------------------
| WEBSITE:      http://inilabs.net
| -----------------------------------------------------
*/

/* User Image & Login Start */
.user-logo {
  margin: 0px;
  padding: 0px;
  border-radius: 50%;
  -moz-border-radius: 50%;
  -webkit-border-radius: 50%;
  height: 30px;
  width: 30px;
  margin-right: 0px;
  padding: 0px 0px 0px 0px;
}

.navbar-nav > .user-menu > .dropdown-menu > li.user-body > div > a:hover {
 color: #366873;
}

.navbar-nav > .user-menu > .dropdown-menu {
  border: 0px solid #FFFFFF;
  border-bottom-left-radius: 5px;
  border-bottom-right-radius: 5px;
  -webkit-border-bottom-left-radius: 5px;
  -webkit-border-bottom-right-radius: 5px;
  -moz-border-bottom-left-radius: 5px;
  -moz-border-bottom-right-radius: 5px;
}

.navbar-nav > .user-menu > .dropdown-menu > li.user-footer > div.text-center > a {
  color: #fff;
}
/* User Image & Login Close */

/* Language Start */
.language-img {
  margin: 0px;
  padding: 0px;
  height: 14px;
  width: 22px;
  padding: 0px 0px 0px 0px;
}

.navbar-nav > .notifications-menu > .dropdown-menu > li .menu > li > a > div > img {
  border: 1px solid #dddddd;
  height: 20px;
  margin: -10px 10px auto 0px;
  width: 30px;
}
.navbar-nav > .notifications-menu > .dropdown-menu > li .menu > li > a > h4 {
  font-size: 12px;
  margin: 0;
  padding: 0px;
}

.navbar-nav > .notifications-menu > .dropdown-menu {
  width: 170px;
}
/* Labguage Close */

/* Menu User Image Start */
.color-green {
  color: #00acac;
}
/* Menu User Image End */

/* Side Menu  Start */
.sidebar .sidebar-menu > li > a:hover {
  background-color:  #2196f3 !important;
  color: #FFFFFF !important;
}
.sidebar .sidebar-menu .treeview-menu > li > a:hover {
  background-color: rgba(255, 255, 255, 0.05);
  color: #FFFFFF;
}
/* Side Menu Close */

/* Breadcrumb Start */
.box > .box-header > .breadcrumb {
  background: none !important;
  float: right;
  padding: 14px;
  margin-bottom: 0px;
  position: relative;
  font-size: 12px;
}
/* Breadcrumb Close */

/* Page Header Add Module */
.page-header > a {
  font-size: 14px;
  border-bottom: 1px solid #707478;
  color: #707478;
}

.page-header > a:hover {
  border-bottom: 1px solid #1A2229;
  color: #1A2229;
}
/* page Header Add Module */

/* Lable Start */
.form-horizontal .control-label {
  text-align: left;
  font-size: 12px;
}

label {
  font-weight: 000;
  color: #707478;
}
/* Lable Close */

/* From span Start*/
.form-horizontal > .form-group > span {
  font-weight: 000;
  color: #FF0B07;
}
/*  From span Close */

/* Table start */
.table-bordered {
    border: 1px solid #E2E7EB;
}
.table-bordered > thead > tr > th {
  border-bottom-width: 2px;
  color: #1A2229;
  font-weight: 300;
}
.table-bordered > tbody > tr > td {
  color: #707070;
}
.table-bordered > thead > tr > th,
.table-bordered > tbody > tr > td {
  border-color: #E2E7EB;
  font-size: 12px;
}

.pagination > .disabled > span,
.pagination > .disabled > span:hover,
.pagination > .disabled > span:focus,
.pagination > .disabled > a,
.pagination > .disabled > a:hover,
.pagination > .disabled > a:focus  {
  border-color: #E2E7EB;
}

.pagination > .active > a,
.pagination > .active > span,
.pagination > .active > a:hover,
.pagination > .active > span:hover,
.pagination > .active > a:focus,
.pagination > .active > span:focus {
  border-color: #242A30;
  background-color: #242A30;
}

.pagination > li > a,
.pagination > li > span {
  border-color: #E2E7EB;
}
/* Table Close */

/* Login Start */
.white-bg-login {
    background: none repeat scroll 0 0 #F1F2F7;
}

.form-box > .header {
  margin-top: 120px;
}

.form-box .body, .form-box .footer {
  border-radius: 0px 0px 4px 4px;
  -moz-border-radius: 0px 0px 4px 4px;
  -webkit-border-radius: 0px 0px 4px 4px;
}

.form-box .body > .form-group > input,
.form-box .footer > .form-group > input {
    border: 1px solid #E2E7EB;
    box-shadow: 0 0 0 rgba(0, 0, 0, 0.070) inset;
}

.checkbox > label > input[type="checkbox"] {
    float: left;
    padding: 0px;
    margin: 2px 0px 0px 0px;
}

.checkbox > label > span {
  color:#a4b1c2;
}

.checkbox > span > label > a {
  color:#a4b1c2;
}
/* Login Close */

/* Button Margin ex: edit, delete, view Start */
.mrg {
  margin-left: 2px;
  margin-right: 2px;
}
/* Button Margin ex: edit, delete, view Close */

/* File Browse Start */
.fileUpload {
    margin: 0;
    overflow: hidden;
    position: relative;
}

.fileUpload input.upload {
    position: absolute;
    top: 0;
    right: 0;
    margin: 0;
    padding: 0;
    font-size: 20px;
    cursor: pointer;
    opacity: 0;
    filter: alpha(opacity=0);
}

.file {
  visibility: hidden;
  position: absolute;
}

.input-file { position: relative; margin: 60px 60px 0 } /* Remove margin, it is just for stackoverflow viewing */
.input-file .input-group-addon { border: 0px; padding: 0px; }
.input-file .input-group-addon .btn { border-radius: 0 4px 4px 0 }
.input-file .input-group-addon input { cursor: pointer; position:absolute; width: 72px; z-index:2;top:0;right:0;filter: alpha(opacity=0);-ms-filter:"progid:DXImageTransform.Microsoft.Alpha(Opacity=0)";opacity:0; background-color:transparent; color:transparent; }

/* File Browse Close */

/* Fine Total Start */
.total {
    float: right;
    border:1px solid #ccc;
    padding:10px;
    background-color: #142;
    border-radius: 0px;
    -moz-border-radius: 0px;
    -webkit-border-radius: 0px;
}

.total-marg {
  padding-left: 0px;
  padding-right: 0px;
}
/* Fine Total Close */

/* Biograph Start */
.profile-view-head {
  background-image: url('../../../../uploads/default/pf.jpg');
    color: #fff;
    text-align: center;
    padding: 10px;
    border-radius: 4px 4px 0 0;
    -webkit-border-radius: 4px 4px 0 0;
    font-size: 16px;
    font-weight: 300;
}

.profile-view-head  a  {
    border-radius: 50%;
    -webkit-border-radius: 50%;
    border: 10px solid rgba(256,256,256,0.3);
    display: inline-block;
}

.profile-view-head a img {
    width: 112px;
    height: 112px;
    border-radius: 50%;
    -webkit-border-radius: 50%;
}

.profile-view-head h1 {
    font-size: 22px;
    font-weight: 300;
    margin-bottom: 5px;
}

.profile-view-head p {
    font-size: 14px;
}

.profile-view-dis {
    color: #89817e;
}

.profile-view-dis h1 {
    font-size: 22px;
    font-weight: 300;
    margin: 0 0 20px;
}

.profile-view-tab {
    width: 50%;
    float: left;
    margin-bottom: 10px;
    padding:0 15px;
    font-size: 14px;
}

.profile-view-tab p span {
    display: inline-block;
    width: 40%;
}

@media screen and (max-width: 480px) {
    .profile-view-tab {
        width: 100%;
    }
}

/* Biograph Close */

/* Profile Graph Start */
.profile-db-head {
    background: #009688;
    color: #fff;
    text-align: center;
    font-style: italic;
    padding: 5px;
    border-radius: 4px 4px 0 0;
    -webkit-border-radius: 4px 4px 0 0;
    font-size: 16px;
    font-weight: 300;
}

.profile-db-head  a  {
    border-radius: 50%;
    -webkit-border-radius: 50%;
    border: 5px solid rgba(256,256,256,0.3);
    display: inline-block;
}

.profile-db-head a img {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    -webkit-border-radius: 50%;
}

.profile-db-head h1 {
    font-size: 16px;
    font-weight: 300;
    margin-bottom: 5px;
}

.profile-db-head p {
    font-size: 12px;
}

/* Profile Graph End */

/* View Button Start */
.btn-cs {
  background-color: #FF766C;
  color: #FFFFFF;
  border: 1px solid transparent;
  border-radius: 4px;
  cursor: pointer;
  display: inline-block;
  font-size: 14px;
  font-weight: normal;
  line-height: 1.42857;
  margin: 2px;
  padding: 6px 12px;
  text-align: center;
  vertical-align: middle;
  white-space: nowrap;
}

.btn-cs:hover {
  background-color: #ED857E;
  color: #EDEDED;
}

.btn-sm-cs {
    border-radius: 3px;
    font-size: 12px;
    line-height: 1.5;
    padding: 5px 10px;
}
/* View Button Close */

/* IDcard Start */

.idcard-Table {
  background-color: #FF766C;
  width: 100%;
  border-top-left-radius: 60px;
  border-bottom-left-radius: 60px;
  border-top-right-radius: 5px;
  border-bottom-right-radius: 5px;

  -moz-border-top-left-radius: 60px;
  -moz-border-bottom-left-radius: 60px;
  -moz-border-top-right-radius: 5px;
  -moz-border-bottom-right-radius: 5px;

  -webkit-border-top-left-radius: 60px;
  -webkit-border-bottom-left-radius: 60px;
  -webkit-border-top-right-radius: 5px;
  -webkit-border-bottom-right-radius: 5px;
}

.idcard-Table td {
  width: 40%;
}

.idcard-Table h4 {
  margin: 0px;
  padding: 0px;
}

.idcard-Table img {
  border-radius: 50%;
  width: 110px;
  height: 110px;
  border: 8px solid #FF9F98;
}

.idcard-Table .row-style {
  color: #FFFFFF;
  vertical-align: top;
  margin:0px;
  padding:0px;
}

/* IDcard Close */

/* View breadcrumb Start */
.well .breadcrumb {
  float: right;
  margin-bottom: 0px;
}
/* View breadcrumb Close */


/* Dashboard Start */

.panel > .table > tbody > tr {
  border-top: 1px solid #EEEEEE;
  height: 45px;
}

.panel > .table > tbody > tr > td {
  line-height: 22px;
}

/* Dashboard Close */

/* Library book sugation Start */

.book {
  display: none;
  z-index: 1000000;
  position: fixed;
  clear: both;
  margin: 0px;
  padding: 0px;
  border-left: 1px solid #e2e7eb;
  border-right: 1px solid #e2e7eb;
  border-bottom: 1px solid #e2e7eb;
  background-color: #fff;
  -webkit-box-shadow: -1px 3px 11px 0px rgba(42, 50, 50, 0.57);
  -moz-box-shadow:    -1px 3px 11px 0px rgba(42, 50, 50, 0.57);
  box-shadow:         -1px 3px 11px 0px rgba(42, 50, 50, 0.57);
}

.result {
  margin: 0;
  padding: 0;
  border: 0;
  list-style: none;
}

.result li {
  padding: 5px;
  border-top: 0;
  cursor: pointer;
}

.result li:hover {
  background: #e2e7eb;
  color: #555555;
}

/* Library book sugation Start */

/* Start tag border of mail and sms template */
.border {
  border: 1px solid #e2e7eb;
  padding: 1px 1px 1px 0px;
  min-height: 70px;
  max-height: auto;
}

.email_alltag {
  margin: 1px 1px 1px 0px;
}

.sms_alltag {
  margin: 1px 1px 1px 0px;
}
/* Close tag border of mail and sms template */

/* Start for Holiday and Event */
.profile-view-head-cover {
  min-height: 300px;
  background-color: #b5bbc8;
  background-attachment: fixed;
  background-size: cover;
  background-repeat: no-repeat;
  border-radius: 5px 5px 0 0;
}

.picture-left {
  width: 70px;
  height: 70px;
  background-color: #f0f3f5;
  font-size: 22px;
  text-align: center;
  color: gray;
  border-radius: 8px;
  font-weight: bold;
  line-height: 1.42857;
  letter-spacing: 3px;
  float: left;
  margin-left: 3%;
  margin-top: 263px;
  padding: 2px;
}

.picture-right {
  width: 70px;
  height: 70px;
  background-color: #f0f3f5;
  font-size: 22px;
  text-align: center;
  color: gray;
  border-radius: 8px;
  font-weight: bold;
  line-height: 1.42857;
  letter-spacing: 3px;
  float: right;
  margin-right: 3%;
  margin-top: 263px;
  padding: 2px;
}
/* Close for Holiday and Event */

/* Start Onoff Small switch */
.onoffswitch-small {
    position: relative; width: 60px;
    -webkit-user-select:none; -moz-user-select:none; -ms-user-select: none;
}
.onoffswitch-small-checkbox {
    display: none;
}
.onoffswitch-small-label {
    display: block; overflow: hidden; cursor: pointer;
    border: 2px solid #999999; border-radius: 10px;
}
.onoffswitch-small-inner {
    display: block; width: 200%; margin-left: -100%;
    transition: margin 0.3s ease-in 0s;
}
.onoffswitch-small-inner:before, .onoffswitch-small-inner:after {
    display: block; float: left; width: 50%; height: 18px; padding: 0; line-height: 18px;
    font-size: 10px; color: white; font-family: Trebuchet, Arial, sans-serif; font-weight: bold;
    box-sizing: border-box;
}
.onoffswitch-small-inner:before {
    content: "ON";
    padding-left: 9px;
    background-color: #34A7C1; color: #FFFFFF;
}
.onoffswitch-small-inner:after {
    content: "OFF";
    padding-right: 9px;
    background-color: #EEEEEE; color: #999999;
    text-align: right;
}
.onoffswitch-small-switch {
    display: block; width: 12px; margin: 3px;
    background: #FFFFFF;
    position: absolute; top: 0; bottom: 0;
    right: 38px;
    border: 2px solid #999999; border-radius: 10px;
    transition: all 0.3s ease-in 0s;
}
.onoffswitch-small-checkbox:checked + .onoffswitch-small-label .onoffswitch-small-inner {
    margin-left: 0;
}
.onoffswitch-small-checkbox:checked + .onoffswitch-small-label .onoffswitch-small-switch {
    right: 0px;
}
/* Close Onoff Small switch */

.input-group-addon.btn.btn-success > a {
    color: #fff;
}

.input-group-addon.btn.btn-success {
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
}

.image-preview-input {
    position: relative;
    overflow: hidden;
    margin: 0px;
    color: #333;
    background-color: #fff;
    border-color: #ccc;
}
.image-preview-input input[type=file] {
  position: absolute;
  top: 0;
  right: 0;
  margin: 0;
  padding: 0;
  font-size: 20px;
  cursor: pointer;
  opacity: 0;
  filter: alpha(opacity=0);
  overflow: hidden;
}
.image-preview-input-title {
    margin-left:2px;
    overflow: hidden;
}

.input-group-addon.btn.btn-danger {
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
}

.input-group-addon.btn.btn-danger > a {
  color: #FFF;
  padding: 0 5px;
}

/* Start Default Button */
.input-group-addon.btn.btn-default {
    border-bottom-right-radius: 3px;
    border-top-right-radius: 3px;
}

.input-group-addon.btn.btn-default > a {
  color: #707478;
  padding: 0 5px;
}

/* Close Default Button Close */

.glyphicon.glyphicon-ok {
    font-size: 12px;
}

.hide-dropdown-icon {
   -moz-appearance:none;
  -webkit-appearance:none;
}

.margin-top-bottom {
  padding-left:0px;
  padding-right:0px;
}

.drop-marg {
  border: 1px solid #09A3A3;
  padding-left:0px;
  padding-right:0px;
}

.invoice-td {
  text-align: right;
}

.bg-sky {
  background-color: #98B2CC;
}

.bg-sky-light {
  background-color: #89A0B8;
}

.bg-purple-shipu {
  background-color: #9583AC;
}

.bg-sky-total {
  background-color: #8083AC;
}

.bg-sky-total-grade {
  background-color: #956785;
}

.bg-black-btn, .bg-black-btn:active, .bg-black-btn:visited, .bg-black-btn:link {
  background-color: #23292F;
  color: #FFF;
}

.bg-black-btn:hover {
  background-color: #333C45;
  color: #CCC;
}

.bg-maroon-light {
  background-color: #C24984 !important;
  color: #fff !important;
}

.bg-maroon-dark {
  background-color: #d81b60 !important;
  color: #f9f9f9 !important;
}

.text-maroon-light {
    color: #C24984;
}

.bg-blue-dark {
    background-color: #3A87AD !important;
    color: #f9f9f9 !important;
}

.bg-blue-light {
    background-color: #5C6BC0 !important;
    color: #f9f9f9 !important;
}

.bg-purple-light {
    background-color: #9575CD !important;
    color: #fff !important;
}

.bg-pink-light {
    background-color: #F06292 !important;
    color: #fff !important;
}

.bg-teal-light {
    background-color: #4DB6AC !important;
    color: #fff !important;
}

.bg-orange-dark {
    background-color: #FF8A65 !important;
    color: #fff !important;
}

@media print
{
    .no-print, .no-print *
    {
        display: none !important;
    }
}
/*.modal-open .modal .tooltip{z-index:2080;}*/



/* activities image selected css*/
.act-image > img {margin:10px;}
.act-image .selected {
    border: 2px solid #0A75AF;
}
.act-attach {
    border: 2px solid #1f3346;
    padding: 5px;
    height:107.5px;
    width: 107.5px;
    margin: 2px;
    -webkit-filter: grayscale(40%);
    filter:  grayscale(40%);
}
.thumbnail-attach:hover {
    opacity: 0.3;
}
.box-header > .box-tools {
    position: absolute;
    right: 10px;
    top: 5px;
}
.box-header > .box-tools [data-toggle="tooltip"] {
    position: relative;
}
.btn-box-tool {
    background: transparent none repeat scroll 0 0;
    color: #97a0b3;
    font-size: 12px;
    padding: 5px;
}




/* INILabs Code start here */
.modal-open .modal .tooltip{z-index:2080;}

/* Social media activities css*/
.box-widget {
    border: medium none;
    position: relative;
}
.box-header.with-border {
    border-bottom: 1px solid #f4f4f4;
}
.user-block::before, .user-block::after {
    content: " ";
    display: table;
}
.user-block::after {
    clear: both;
}
.user-block::before, .user-block::after {
    content: " ";
    display: table;
}
.box-comments {
    background: #f7f7f7 none repeat scroll 0 0;
}
.box-comments .box-comment::before, .box-comments .box-comment::after {
    content: " ";
    display: table;
}
.box-comments .box-comment::after {
    clear: both;
}
.box-comments .box-comment:first-of-type {
    padding-top: 0;
}
.box-comments .box-comment:last-of-type {
    border-bottom: 0 none;
}
.box-comments .box-comment {
    border-bottom: 1px solid #eee;
    padding: 8px 0;
}
.img-sm, .box-comments .box-comment img, .user-block.user-block-sm img {
    height: 30px;
    width: 30px;
}
.img-sm, .img-md, .img-lg, .box-comments .box-comment img, .user-block.user-block-sm img {
    float: left;
}
.box-comments .comment-text {
    color: #555;
    margin-left: 40px;
}
.box-comments .username {
    color: #444;
    display: block;
    font-weight: 600;
}
.box-comments .text-muted {
    font-size: 12px;
    font-weight: 400;
}
.box-comments {
    background: #f7f7f7 none repeat scroll 0 0 !important;
}
.img-sm, .box-comments .box-comment img, .user-block.user-block-sm img {
    height: 30px;
    width: 30px;
}
.img-sm, .img-md, .img-lg, .box-comments .box-comment img, .user-block.user-block-sm img {
    float: left;
}
.img-sm + .img-push {
    margin-left: 40px;
}
.user-block img {
    float: left;
    height: 40px;
    width: 40px;
}
.box-widget .box-header {
    color: #444;
    display: block;
    padding: 10px;
    position: relative;
}
.user-block .username {
    font-size: 16px;
    font-weight: 600;
}
.user-block .username, .user-block .description, .user-block .comment {
    display: block;
    margin-left: 50px;
}
.social-media {
    background-color: #FFFFFF !important;
}
.list-unstyled, .chart-legend, .contacts-list, .users-list, .mailbox-attachments {
    list-style: outside none none;
    margin: 0;
    padding: 0;
}
.users-list > li {
    float: left;
    padding: 10px;
    text-align: center;
    width: 25%;
}
.users-list > li img {
    border-radius: 50%;
    height: auto;
    max-width: 100%;
}
.users-list-name {
    color: #444;
    font-weight: 600;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}
.users-list-name, .users-list-date {
    display: block;
}
.users-list-date {
    color: #999;
    font-size: 12px;
}

/*tooptip*/
.tooltip_content { display: none; }

/* activities image selected css*/
.act-image > img {margin:10px;}
.act-image .selected {
    border: 2px solid #0A75AF;
}
.act-attach {
    border: 2px solid #1f3346;
    padding: 5px;
    height:107.5px;
    width: 107.5px;
    margin: 2px;
    -webkit-filter: grayscale(40%);
    filter:  grayscale(40%);
}
.thumbnail-attach:hover {
    opacity: 0.3;
}
.box-header > .box-tools {
    position: absolute;
    right: 10px;
    top: 5px;
}
.box-header > .box-tools [data-toggle="tooltip"] {
    position: relative;
}
.btn-box-tool {
    background: transparent none repeat scroll 0 0;
    color: #97a0b3;
    font-size: 12px;
    padding: 5px;
}

.activity-padd-left {
  padding-left: 0;
}


/* INILabs Code Colose Here */

/* School 3.6 Update Css Start*/
.global_payment_search {
  margin-top: 22px;
}

.feesPaymentInfo {
  border: 1px solid #E2E7EB;
}

.feesPaymentInfo .info {
  padding-left:0px !important;
  padding-right:15px !important; 
  padding-top: 10px; 
  padding-bottom: 10px;
}

.feesPaymentInfo .vl {
  border-left: 1px solid #E2E7EB;
  height: 100%;
  display: inline-block;
  position: absolute  
}

.feesPaymentInfo .invoice-table-responsive {
  padding-top: 15px; 
  padding-left: 15px; 
  padding-right: 0px;
}

@media screen and (max-width: 480px) {
  .feesPaymentInfo .vl {
    border-top: 1px solid #E2E7EB;
    display: inline-block;
    width: 100%;
    position: relative; 
  }

  .feesPaymentInfo .info {
    padding-left: 0px !important;
    padding-right: 0px !important;
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .global_payment_search {
    margin-bottom: 22px;
  }

  .feesPaymentInfo .invoice-table-responsive {
    padding-top: 15px; 
    padding-left: 0px; 
    padding-right: 0px;
  }
}

@media screen and (max-width: 767px) {
  .feesPaymentInfo .vl {
    border-top: 1px solid #E2E7EB;
    display: inline-block;
    width: 100%;
    position: relative; 
  }

  .feesPaymentInfo .info {
    padding-left: 0px !important;
    padding-right: 0px !important;
    padding-top: 10px;
    padding-bottom: 10px;
  }

  .global_payment_search {
    margin-bottom: 22px;
  }

  .feesPaymentInfo .invoice-table-responsive {
    padding-top: 15px; 
    padding-left: 0px; 
    padding-right: 0px;
  }
}

@media screen and (max-width: 992px) {
  .global_payment_search {
    margin-bottom: 22px;
  }
}

.errorClass {
  border: 1px solid #FB3A2C;
  line-height: 0px;
}

.errorClass input[type="checkbox"] {
  margin: 0px;
}

.ini-bg-success {
  background: #5eb171!important;
  color:#232a2f !important;
}

.ini-bg-primary {
  background: #6a94c1!important;
  color:#232a2f !important;
}

.ini-bg-danger {
  background: #e8737e!important;
  color:#232a2f !important;
}

.ini-bg-warning {
  background: #ffc107!important;
  color:#232a2f !important;
}

.ini-bg-info {
  background: #5fbfce!important;
  color:#232a2f !important;
}

.ini-bg-secondary {
  background: #8cb0d0!important;
  color:#232a2f !important;
}

.attendance_table {
  width:100%;
}

.attendance_table tr td{
  text-align: center;
  border:0.1px solid #ddd;
  padding: 4px 0px; 
  color: #232a2f !important;
} 

.attendance_table tr th{
  text-align: center;
  border:0.1px solid #ddd;
  color: #232a2f;
  font-weight: 400;
  padding: 4px 0px; 
} 

.profile-user-img {
  margin: 0 auto;
  width: 100px;
  height: 100px;
  padding: 3px;
  border: 3px solid #d2d6de;
}

.profile-username {
  font-size: 21px !important;
  margin-top: 5px;
}

.ini-text-present {
  color: green !important;
}

.ini-text-late {
  color: green !important;
}

.ini-text-lateex {
  color: green !important;
}

.ini-text-absent {
  color: red !important;
}

.ini-text-holiday {
  color: orange !important;
}

.ini-text-weekenday {
  color: #2032EA !important;
}

.ini-text-not-assign {
  color: #555 !important;
}

.attendance_table tr td.text-left{
  text-align: left !important;
  padding-left: 3px !important;
}

.footerAll {
  margin-top: 10px;
}

.activity-icon-box {
  position: absolute;
  z-index: 5555;
  border: 1px solid #E315D6;
  border-radius: 3px;
  color: #E315D6;
  padding: 5px;
  margin-left: 18px;
  margin-top: 18px;
}

.activity-icon-box i {
  font-size: 20px;
  text-align: center;
  position: relative;
  display: inline-block;
}

/* School 3.6 Update Css Close*/

/* School 4.1 Update Css Start*/
.media-file-propractice-for-media {
  display: inline-block;
  font-size: 10px;
}


.table.table-bordered.product-style > thead > tr > th, .table.table-bordered.product-style > tbody > tr > td, .table.table-bordered.product-style > tfoot > tr > td {
  font-size: 14px;
  text-align: center;
}

.table.table-bordered.product-style > thead > tr > th {
  background-color: #00acac;
  color: #fff;
  font-weight: bold;
}

.table.table-bordered.product-style > thead > tr > th, .table.table-bordered.product-style > tbody > tr > td {
  padding: 4px 8px !important;
  line-height: 2.2;
} 

.table.table-bordered.product-style > thead > tr > th > .form-control, .table.table-bordered.product-style > tbody > tr > td > .form-control {
  text-align: center;
}

.invoice-col .refund {
  border:3px solid #ccc;
  margin-top:10px;
  text-align: center; 
  height:60px;
  line-height: 60px;
  border-radius: 5px;
  font-size: 24px;
  transform: rotate(25deg);
  color:#ff0000;
  font-weight: bold; 
  width:80%;
}

.col-lg-1.col-mergin, .col-xs-2.col-mergin, .col-sm-2.col-mergin, .col-md-2.col-mergin, .col-lg-2.col-mergin, .col-xs-3.col-mergin, .col-sm-3.col-mergin, .col-md-3.col-mergin, .col-lg-3.col-mergin, .col-xs-4.col-mergin, .col-sm-4.col-mergin, .col-md-4.col-mergin, .col-lg-4.col-mergin, .col-xs-5.col-mergin, .col-sm-5.col-mergin, .col-md-5.col-mergin, .col-lg-5.col-mergin, .col-xs-6.col-mergin, .col-sm-6.col-mergin, .col-md-6.col-mergin, .col-lg-6.col-mergin, .col-xs-7.col-mergin, .col-sm-7.col-mergin, .col-md-7.col-mergin, .col-lg-7.col-mergin, .col-xs-8.col-mergin, .col-sm-8.col-mergin, .col-md-8.col-mergin, .col-lg-8.col-mergin, .col-xs-9.col-mergin, .col-sm-9.col-mergin, .col-md-9.col-mergin, .col-lg-9.col-mergin, .col-xs-10.col-mergin, .col-sm-10.col-mergin, .col-md-10.col-mergin, .col-lg-10.col-mergin, .col-xs-11.col-mergin, .col-sm-11.col-mergin, .col-md-11.col-mergin, .col-lg-11.col-mergin, .col-xs-12.col-mergin, .col-sm-12.col-mergin, .col-md-12.col-mergin, .col-lg-12.col-mergin {
  margin-left: -15px;
  margin-right: -15px;
}

.box.box-height {
  min-height: 350px; 
}

@media screen and (max-width: 992px) {
  .main-footer {
    margin-left: 0px;
  }
  .wrapper.active .main-footer {
    margin-left: 220px;
  }
}

@media screen and (max-width: 767px) {
  .col-lg-1.col-mergin, .col-xs-2.col-mergin, .col-sm-2.col-mergin, .col-md-2.col-mergin, .col-lg-2.col-mergin, .col-xs-3.col-mergin, .col-sm-3.col-mergin, .col-md-3.col-mergin, .col-lg-3.col-mergin, .col-xs-4.col-mergin, .col-sm-4.col-mergin, .col-md-4.col-mergin, .col-lg-4.col-mergin, .col-xs-5.col-mergin, .col-sm-5.col-mergin, .col-md-5.col-mergin, .col-lg-5.col-mergin, .col-xs-6.col-mergin, .col-sm-6.col-mergin, .col-md-6.col-mergin, .col-lg-6.col-mergin, .col-xs-7.col-mergin, .col-sm-7.col-mergin, .col-md-7.col-mergin, .col-lg-7.col-mergin, .col-xs-8.col-mergin, .col-sm-8.col-mergin, .col-md-8.col-mergin, .col-lg-8.col-mergin, .col-xs-9.col-mergin, .col-sm-9.col-mergin, .col-md-9.col-mergin, .col-lg-9.col-mergin, .col-xs-10.col-mergin, .col-sm-10.col-mergin, .col-md-10.col-mergin, .col-lg-10.col-mergin, .col-xs-11.col-mergin, .col-sm-11.col-mergin, .col-md-11.col-mergin, .col-lg-11.col-mergin, .col-xs-12.col-mergin, .col-sm-12.col-mergin, .col-md-12.col-mergin, .col-lg-12.col-mergin {
    margin-left: 0px;
    margin-right: 0px;
  }

  .box.box-height {
    min-height: auto; 
  }
}

@media screen and (max-width: 560px) {
  .col-lg-1.col-mergin, .col-xs-2.col-mergin, .col-sm-2.col-mergin, .col-md-2.col-mergin, .col-lg-2.col-mergin, .col-xs-3.col-mergin, .col-sm-3.col-mergin, .col-md-3.col-mergin, .col-lg-3.col-mergin, .col-xs-4.col-mergin, .col-sm-4.col-mergin, .col-md-4.col-mergin, .col-lg-4.col-mergin, .col-xs-5.col-mergin, .col-sm-5.col-mergin, .col-md-5.col-mergin, .col-lg-5.col-mergin, .col-xs-6.col-mergin, .col-sm-6.col-mergin, .col-md-6.col-mergin, .col-lg-6.col-mergin, .col-xs-7.col-mergin, .col-sm-7.col-mergin, .col-md-7.col-mergin, .col-lg-7.col-mergin, .col-xs-8.col-mergin, .col-sm-8.col-mergin, .col-md-8.col-mergin, .col-lg-8.col-mergin, .col-xs-9.col-mergin, .col-sm-9.col-mergin, .col-md-9.col-mergin, .col-lg-9.col-mergin, .col-xs-10.col-mergin, .col-sm-10.col-mergin, .col-md-10.col-mergin, .col-lg-10.col-mergin, .col-xs-11.col-mergin, .col-sm-11.col-mergin, .col-md-11.col-mergin, .col-lg-11.col-mergin, .col-xs-12.col-mergin, .col-sm-12.col-mergin, .col-md-12.col-mergin, .col-lg-12.col-mergin {
    margin-left: 0px;
    margin-right: 0px;
  }
  .box.box-height {
    min-height: auto; 
  }
}


@media screen and (max-width: 480px) {
  .col-lg-1.col-mergin, .col-xs-2.col-mergin, .col-sm-2.col-mergin, .col-md-2.col-mergin, .col-lg-2.col-mergin, .col-xs-3.col-mergin, .col-sm-3.col-mergin, .col-md-3.col-mergin, .col-lg-3.col-mergin, .col-xs-4.col-mergin, .col-sm-4.col-mergin, .col-md-4.col-mergin, .col-lg-4.col-mergin, .col-xs-5.col-mergin, .col-sm-5.col-mergin, .col-md-5.col-mergin, .col-lg-5.col-mergin, .col-xs-6.col-mergin, .col-sm-6.col-mergin, .col-md-6.col-mergin, .col-lg-6.col-mergin, .col-xs-7.col-mergin, .col-sm-7.col-mergin, .col-md-7.col-mergin, .col-lg-7.col-mergin, .col-xs-8.col-mergin, .col-sm-8.col-mergin, .col-md-8.col-mergin, .col-lg-8.col-mergin, .col-xs-9.col-mergin, .col-sm-9.col-mergin, .col-md-9.col-mergin, .col-lg-9.col-mergin, .col-xs-10.col-mergin, .col-sm-10.col-mergin, .col-md-10.col-mergin, .col-lg-10.col-mergin, .col-xs-11.col-mergin, .col-sm-11.col-mergin, .col-md-11.col-mergin, .col-lg-11.col-mergin, .col-xs-12.col-mergin, .col-sm-12.col-mergin, .col-md-12.col-mergin, .col-lg-12.col-mergin {
    margin-left: 0px;
    margin-right: 0px;
  }

  .box.box-height {
    min-height: auto; 
  }
}

.table.table-bordered.feetype-style > thead > tr > th, .table.table-bordered.feetype-style > tbody > tr > td, .table.table-bordered.feetype-style > tfoot > tr > td {
  font-size: 14px;
  text-align: center;
}

.table.table-bordered.feetype-style > thead > tr > th {
  background-color: #00acac;
  color: #fff;
  font-weight: bold;
}

.table.table-bordered.feetype-style > thead > tr > th, .table.table-bordered.feetype-style > tbody > tr > td {
  padding: 4px 8px !important;
  line-height: 2.2;
} 

.table.table-bordered.feetype-style > thead > tr > th > .form-control, .table.table-bordered.feetype-style > tbody > tr > td > .form-control {
  text-align: center;
}

input.bordered-red {
  border-color: #f56954;
}

.card-image {
  display: block;
  width: 35px; 
  height: 35px;
  background: #fff center center no-repeat;
  background-size: cover;
  filter: blur(3px);
}

.card-image > img {
  display: block;
  width: 35px;
  height: 35px;
  opacity: 0;
}

.card-image.is-loaded {
  filter: none;
  transition: filter 1s;
}

.totalattendanceCount {
  padding-top: 5px;
  font-size: 13px;
}

.single_ebooks .thumbnail {
  margin-bottom: 0px;
}

.single_ebooks .thumbnail img {
  height: 240px;
  width: 100%;
}

.single_ebooks p { 
  margin: 0px; 
}

.single_ebooks {
  position:  relative;
  overflow: hidden;
  margin-bottom: 15px;
}

.single_ebooks ul {
  left: 0;
  right: 0;
  top: 0;
  opacity: 0;
  z-index: 10;
  width: 100%;
  height: 100%;
  display: flex;
  padding-left: 0px;
  list-style: none;
  position: absolute;
  visibility: hidden;
  justify-content: center;
  align-items: center;
  -webkit-transition: all 0.3s linear 0s;
  -moz-transition: all 0.3s linear 0s;
  -ms-transition: all 0.3s linear 0s;
  -o-transition: all 0.3s linear 0s;
  transition: all 0.3s linear 0s;
  background-color: rgba(0, 0, 0, 0.4);
  -webkit-transform: scale(0);
  -moz-transform: scale(0);
  -ms-transform: scale(0);
  -o-transform: scale(0);
  transform: scale(0);
}

.single_ebooks ul li {
  font-size: 20px;
  margin:0px 3px;
}

.single_ebooks ul li a {
  color: #fff;
  background: #ee9234;
  padding: 2px 5px;
  margin: 0px;
}

.single_ebooks:hover ul {
  left: 0;
  opacity: 1;
  margin: 0px;
  right: auto;
  visibility: visible;
  -webkit-transition: all 0.3s linear 0s;
  -moz-transition: all 0.3s linear 0s;
  -ms-transition: all 0.3s linear 0s;
  -o-transition: all 0.3s linear 0s;
  transition: all 0.3s linear 0s;
  -webkit-transform: scale(1.1);
  -moz-transform: scale(1.1);
  -ms-transform: scale(1.1);
  -o-transform: scale(1.1);
  transform: scale(1.1);
}

.single_ebooks:hover ul li a {
  border-radius: 2px;
}
/* School 4.1 Update Css Close*/

