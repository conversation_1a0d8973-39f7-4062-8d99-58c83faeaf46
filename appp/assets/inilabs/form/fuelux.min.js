/*!
 * Fuel UX v3.13.0
 * Copyright 2012-2015 ExactTarget
 * Licensed under the BSD-3-Clause license (https://github.com/ExactTarget/fuelux/blob/master/LICENSE)
 */
!function(a){"function"==typeof define&&define.amd?define(["jquery","bootstrap"],a):a(jQuery)}(function(a){if("undefined"==typeof a)throw new Error("Fuel UX's JavaScript requires jQuery");if("undefined"==typeof a.fn.dropdown||"undefined"==typeof a.fn.collapse)throw new Error("Fuel UX's JavaScript requires Bootstrap");!function(a){var b=a.fn.checkbox,c=function(b,c){if(this.options=a.extend({},a.fn.checkbox.defaults,c),"label"===b.tagName.toLowerCase()){this.$label=a(b),this.$chk=this.$label.find('input[type="checkbox"]'),this.$container=a(b).parent(".checkbox");var d=this.$chk.attr("data-toggle");this.$toggleContainer=a(d),this.$chk.on("change",a.proxy(this.itemchecked,this)),this.setInitialState()}};c.prototype={constructor:c,setInitialState:function(){var a=this.$chk,b=(this.$label,a.prop("checked")),c=a.prop("disabled");this.setCheckedState(a,b),this.setDisabledState(a,c)},setCheckedState:function(a,b){var c=a,d=this.$label,e=(this.$container,this.$toggleContainer);b?(c.prop("checked",!0),d.addClass("checked"),e.removeClass("hide hidden"),d.trigger("checked.fu.checkbox")):(c.prop("checked",!1),d.removeClass("checked"),e.addClass("hidden"),d.trigger("unchecked.fu.checkbox")),d.trigger("changed.fu.checkbox",b)},setDisabledState:function(a,b){var c=this.$label;b?(this.$chk.prop("disabled",!0),c.addClass("disabled"),c.trigger("disabled.fu.checkbox")):(this.$chk.prop("disabled",!1),c.removeClass("disabled"),c.trigger("enabled.fu.checkbox"))},itemchecked:function(b){var c=a(b.target),d=c.prop("checked");this.setCheckedState(c,d)},toggle:function(){var a=this.isChecked();a?this.uncheck():this.check()},check:function(){this.setCheckedState(this.$chk,!0)},uncheck:function(){this.setCheckedState(this.$chk,!1)},isChecked:function(){var a=this.$chk.prop("checked");return a},enable:function(){this.setDisabledState(this.$chk,!1)},disable:function(){this.setDisabledState(this.$chk,!0)},destroy:function(){return this.$label.remove(),this.$label[0].outerHTML}},c.prototype.getValue=c.prototype.isChecked,a.fn.checkbox=function(b){var d,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.checkbox"),h="object"==typeof b&&b;g||f.data("fu.checkbox",g=new c(this,h)),"string"==typeof b&&(d=g[b].apply(g,e))});return void 0===d?f:d},a.fn.checkbox.defaults={},a.fn.checkbox.Constructor=c,a.fn.checkbox.noConflict=function(){return a.fn.checkbox=b,this},a(document).on("mouseover.fu.checkbox.data-api","[data-initialize=checkbox]",function(b){var c=a(b.target);c.data("fu.checkbox")||c.checkbox(c.data())}),a(function(){a("[data-initialize=checkbox]").each(function(){var b=a(this);b.data("fu.checkbox")||b.checkbox(b.data())})})}(a),function(a){var b=a.fn.combobox,c=function(b,c){this.$element=a(b),this.options=a.extend({},a.fn.combobox.defaults,c),this.$dropMenu=this.$element.find(".dropdown-menu"),this.$input=this.$element.find("input"),this.$button=this.$element.find(".btn"),this.$element.on("click.fu.combobox","a",a.proxy(this.itemclicked,this)),this.$element.on("change.fu.combobox","input",a.proxy(this.inputchanged,this)),this.$element.on("shown.bs.dropdown",a.proxy(this.menuShown,this)),this.setDefaultSelection();var d=this.$dropMenu.children("li");0===d.length&&this.$button.addClass("disabled")};c.prototype={constructor:c,destroy:function(){return this.$element.remove(),this.$element.find("input").each(function(){a(this).attr("value",a(this).val())}),this.$element[0].outerHTML},doSelect:function(a){"undefined"!=typeof a[0]?(this.$selectedItem=a,this.$input.val(this.$selectedItem.text().trim())):this.$selectedItem=null},menuShown:function(){this.options.autoResizeMenu&&this.resizeMenu()},resizeMenu:function(){var a=this.$element.outerWidth();this.$dropMenu.outerWidth(a)},selectedItem:function(){var b=this.$selectedItem,c={};if(b){var d=this.$selectedItem.text().trim();c=a.extend({text:d},this.$selectedItem.data())}else c={text:this.$input.val()};return c},selectByText:function(b){var c=a([]);this.$element.find("li").each(function(){return(this.textContent||this.innerText||a(this).text()||"").toLowerCase()===(b||"").toLowerCase()?(c=a(this),!1):void 0}),this.doSelect(c)},selectByValue:function(a){var b='li[data-value="'+a+'"]';this.selectBySelector(b)},selectByIndex:function(a){var b="li:eq("+a+")";this.selectBySelector(b)},selectBySelector:function(a){var b=this.$element.find(a);this.doSelect(b)},setDefaultSelection:function(){var a="li[data-selected=true]:first",b=this.$element.find(a);b.length>0&&(this.selectBySelector(a),b.removeData("selected"),b.removeAttr("data-selected"))},enable:function(){this.$element.removeClass("disabled"),this.$input.removeAttr("disabled"),this.$button.removeClass("disabled")},disable:function(){this.$element.addClass("disabled"),this.$input.attr("disabled",!0),this.$button.addClass("disabled")},itemclicked:function(b){this.$selectedItem=a(b.target).parent(),this.$input.val(this.$selectedItem.text().trim()).trigger("change",{synthetic:!0});var c=this.selectedItem();this.$element.trigger("changed.fu.combobox",c),b.preventDefault(),this.$element.find(".dropdown-toggle").focus()},inputchanged:function(b,c){if(!c||!c.synthetic){var d=a(b.target).val();this.selectByText(d);var e=this.selectedItem();0===e.text.length&&(e={text:d}),this.$element.trigger("changed.fu.combobox",e)}}},c.prototype.getValue=c.prototype.selectedItem,a.fn.combobox=function(b){var d,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.combobox"),h="object"==typeof b&&b;g||f.data("fu.combobox",g=new c(this,h)),"string"==typeof b&&(d=g[b].apply(g,e))});return void 0===d?f:d},a.fn.combobox.defaults={autoResizeMenu:!0},a.fn.combobox.Constructor=c,a.fn.combobox.noConflict=function(){return a.fn.combobox=b,this},a(document).on("mousedown.fu.combobox.data-api","[data-initialize=combobox]",function(b){var c=a(b.target).closest(".combobox");c.data("fu.combobox")||c.combobox(c.data())}),a(function(){a("[data-initialize=combobox]").each(function(){var b=a(this);b.data("fu.combobox")||b.combobox(b.data())})})}(a),function(a){var b="Invalid Date",c="moment.js is not available so you cannot use this function",d=[],e=!1,f=a.fn.datepicker,g=!1,h=function(){var a,b;for(g=!0,a=0,b=d.length;b>a;a++)d[a].init.call(d[a].scope);d=[]};"function"==typeof define&&define.amd?require(["moment"],function(a){e=a,h()},function(a){var b=a.requireModules&&a.requireModules[0];"moment"===b&&h()}):h();var i=function(b,c){this.$element=a(b),this.options=a.extend(!0,{},a.fn.datepicker.defaults,c),this.$calendar=this.$element.find(".datepicker-calendar"),this.$days=this.$calendar.find(".datepicker-calendar-days"),this.$header=this.$calendar.find(".datepicker-calendar-header"),this.$headerTitle=this.$header.find(".title"),this.$input=this.$element.find("input"),this.$wheels=this.$element.find(".datepicker-wheels"),this.$wheelsMonth=this.$element.find(".datepicker-wheels-month"),this.$wheelsYear=this.$element.find(".datepicker-wheels-year"),this.artificialScrolling=!1,this.formatDate=this.options.formatDate||this.formatDate,this.inputValue=null,this.moment=!1,this.momentFormat=null,this.parseDate=this.options.parseDate||this.parseDate,this.preventBlurHide=!1,this.restricted=this.options.restricted||[],this.restrictedParsed=[],this.restrictedText=this.options.restrictedText,this.sameYearOnly=this.options.sameYearOnly,this.selectedDate=null,this.yearRestriction=null,this.$calendar.find(".datepicker-today").on("click.fu.datepicker",a.proxy(this.todayClicked,this)),this.$days.on("click.fu.datepicker","tr td button",a.proxy(this.dateClicked,this)),this.$header.find(".next").on("click.fu.datepicker",a.proxy(this.next,this)),this.$header.find(".prev").on("click.fu.datepicker",a.proxy(this.prev,this)),this.$headerTitle.on("click.fu.datepicker",a.proxy(this.titleClicked,this)),this.$input.on("change.fu.datepicker",a.proxy(this.inputChanged,this)),this.$input.on("mousedown.fu.datepicker",a.proxy(this.showDropdown,this)),this.$wheels.find(".datepicker-wheels-back").on("click.fu.datepicker",a.proxy(this.backClicked,this)),this.$wheels.find(".datepicker-wheels-select").on("click.fu.datepicker",a.proxy(this.selectClicked,this)),this.$wheelsMonth.on("click.fu.datepicker","ul button",a.proxy(this.monthClicked,this)),this.$wheelsYear.on("click.fu.datepicker","ul button",a.proxy(this.yearClicked,this)),this.$wheelsYear.find("ul").on("scroll.fu.datepicker",a.proxy(this.onYearScroll,this));var f=function(){this.checkForMomentJS()&&(e=e||window.moment,this.moment=!0,this.momentFormat=this.options.momentConfig.format,this.setCulture(this.options.momentConfig.culture),e.locale=e.locale||e.lang),this.setRestrictedDates(this.restricted),this.setDate(this.options.date)||(this.$input.val(""),this.inputValue=this.$input.val()),this.sameYearOnly&&(this.yearRestriction=this.selectedDate?this.selectedDate.getFullYear():(new Date).getFullYear())};g?f.call(this):d.push({init:f,scope:this})};i.prototype={constructor:i,backClicked:function(){this.changeView("calendar")},changeView:function(a,b){"wheels"===a?(this.$calendar.hide().attr("aria-hidden","true"),this.$wheels.show().removeAttr("aria-hidden",""),b&&this.renderWheel(b)):(this.$wheels.hide().attr("aria-hidden","true"),this.$calendar.show().removeAttr("aria-hidden",""),b&&this.renderMonth(b))},checkForMomentJS:function(){return(a.isFunction(window.moment)||"undefined"!=typeof e&&a.isFunction(e))&&a.isPlainObject(this.options.momentConfig)&&"string"==typeof this.options.momentConfig.culture&&"string"==typeof this.options.momentConfig.format?!0:!1},dateClicked:function(b){var c,d=a(b.currentTarget).parents("td:first");d.hasClass("restricted")||(this.$days.find("td.selected").removeClass("selected"),d.addClass("selected"),c=new Date(d.attr("data-year"),d.attr("data-month"),d.attr("data-date")),this.selectedDate=c,this.$input.val(this.formatDate(c)),this.inputValue=this.$input.val(),this.hideDropdown(),this.$input.focus(),this.$element.trigger("dateClicked.fu.datepicker",c))},destroy:function(){return this.$element.remove(),this.$days.find("tbody").empty(),this.$wheelsYear.find("ul").empty(),this.$element[0].outerHTML},disable:function(){this.$element.addClass("disabled"),this.$element.find("input, button").attr("disabled","disabled"),this.$element.find(".input-group-btn").removeClass("open")},enable:function(){this.$element.removeClass("disabled"),this.$element.find("input, button").removeAttr("disabled")},formatDate:function(a){var b=function(a){var b="0"+a;return b.substr(b.length-2)};return this.moment?e(a).format(this.momentFormat):b(a.getMonth()+1)+"/"+b(a.getDate())+"/"+a.getFullYear()},getCulture:function(){if(this.moment)return e.locale();throw c},getDate:function(){return this.selectedDate?this.selectedDate:new Date(NaN)},getFormat:function(){if(this.moment)return this.momentFormat;throw c},getFormattedDate:function(){return this.selectedDate?this.formatDate(this.selectedDate):b},getRestrictedDates:function(){return this.restricted},inputChanged:function(){var a,b=this.$input.val();b!==this.inputValue&&(a=this.setDate(b),null===a?this.$element.trigger("inputParsingFailed.fu.datepicker",b):a===!1?this.$element.trigger("inputRestrictedDate.fu.datepicker",a):this.$element.trigger("changed.fu.datepicker",a))},showDropdown:function(a){this.$input.is(":focus")||this.$element.find(".input-group-btn").addClass("open")},hideDropdown:function(){this.$element.find(".input-group-btn").removeClass("open")},isInvalidDate:function(a){var c=a.toString();return c===b||"NaN"===c?!0:!1},isRestricted:function(a,b,c){var d,e,f,g,h=this.restrictedParsed;if(this.sameYearOnly&&null!==this.yearRestriction&&c!==this.yearRestriction)return!0;for(d=0,f=h.length;f>d;d++)if(e=h[d].from,g=h[d].to,(c>e.year||c===e.year&&b>e.month||c===e.year&&b===e.month&&a>=e.date)&&(c<g.year||c===g.year&&b<g.month||c===g.year&&b===g.month&&a<=g.date))return!0;return!1},monthClicked:function(b){this.$wheelsMonth.find(".selected").removeClass("selected"),a(b.currentTarget).parent().addClass("selected")},next:function(){var a=this.$headerTitle.attr("data-month"),b=this.$headerTitle.attr("data-year");if(a++,a>11){if(this.sameYearOnly)return;a=0,b++}this.renderMonth(new Date(b,a,1))},onYearScroll:function(b){if(!this.artificialScrolling){var c,d,e=a(b.currentTarget),f="border-box"===e.css("box-sizing")?e.outerHeight():e.height(),g=e.get(0).scrollHeight,h=e.scrollTop(),i=f/(g-h)*100,j=h/g*100;if(5>j){for(d=parseInt(e.find("li:first").attr("data-year"),10),c=d-1;c>d-11;c--)e.prepend('<li data-year="'+c+'"><button type="button">'+c+"</button></li>");this.artificialScrolling=!0,e.scrollTop(e.get(0).scrollHeight-g+h),this.artificialScrolling=!1}else if(i>90)for(d=parseInt(e.find("li:last").attr("data-year"),10),c=d+1;d+11>c;c++)e.append('<li data-year="'+c+'"><button type="button">'+c+"</button></li>")}},parseDate:function(a){var b,c,d,f,g,h,i,j=this,k=new Date(NaN);if(a){if(this.moment)return f=function(a){var b=e(a,j.momentFormat);return!0===b.isValid()?b.toDate():k},d=function(a){var b=e(new Date(a));return!0===b.isValid()?b.toDate():k},g=function(a,b,c){var d=b(a);return j.isInvalidDate(d)?(d=c(d),j.isInvalidDate(d)?k:d):d},"string"==typeof a?g(a,f,d):g(a,d,f);if("string"==typeof a){if(b=new Date(Date.parse(a)),!this.isInvalidDate(b))return b;if(a=a.split("T")[0],c=/^\s*(\d{4})-(\d\d)-(\d\d)\s*$/,i=c.exec(a),i&&(h=parseInt(i[2],10),b=new Date(i[1],h-1,i[3]),h===b.getMonth()+1))return b}else if(b=new Date(a),!this.isInvalidDate(b))return b}return new Date(NaN)},prev:function(){var a=this.$headerTitle.attr("data-month"),b=this.$headerTitle.attr("data-year");if(a--,0>a){if(this.sameYearOnly)return;a=11,b--}this.renderMonth(new Date(b,a,1))},renderMonth:function(b){b=b||new Date;var c,d,e,f,g,h,i,j,k,l,m,n=new Date(b.getFullYear(),b.getMonth(),1).getDay(),o=new Date(b.getFullYear(),b.getMonth()+1,0).getDate(),p=new Date(b.getFullYear(),b.getMonth(),0).getDate(),q=this.$headerTitle.find(".month"),r=b.getMonth(),s=new Date,t=s.getDate(),u=s.getMonth(),v=s.getFullYear(),w=this.selectedDate,x=this.$days.find("tbody"),y=b.getFullYear();for(w&&(w={date:w.getDate(),month:w.getMonth(),year:w.getFullYear()}),q.find(".current").removeClass("current"),q.find('span[data-month="'+r+'"]').addClass("current"),this.$headerTitle.find(".year").text(y),this.$headerTitle.attr({"data-month":r,"data-year":y}),x.empty(),0!==n?(c=p-n+1,i=-1):(c=1,i=0),h=35-n>=o?5:6,f=0;h>f;f++){for(m=a("<tr></tr>"),g=0;7>g;g++)l=a("<td></td>"),-1===i?(l.addClass("last-month"),j!==i&&l.addClass("first")):1===i&&(l.addClass("next-month"),j!==i&&l.addClass("first")),d=r+i,e=y,0>d?(d=11,e--):d>11&&(d=0,e++),l.attr({"data-date":c,"data-month":d,"data-year":e}),e===v&&d===u&&c===t?l.addClass("current-day"):(v>e||e===v&&u>d||e===v&&d===u&&t>c)&&(l.addClass("past"),this.options.allowPastDates||l.addClass("restricted").attr("title",this.restrictedText)),this.isRestricted(c,d,e)&&l.addClass("restricted").attr("title",this.restrictedText),w&&e===w.year&&d===w.month&&c===w.date&&l.addClass("selected"),l.hasClass("restricted")?l.html('<span><b class="datepicker-date">'+c+"</b></span>"):l.html('<span><button type="button" class="datepicker-date">'+c+"</button></span>"),c++,k=j,j=i,-1===i&&c>p?(c=1,i=0,k!==i&&l.addClass("last")):0===i&&c>o&&(c=1,i=1,k!==i&&l.addClass("last")),f===h-1&&6===g&&l.addClass("last"),m.append(l);x.append(m)}},renderWheel:function(a){var b,c,d,e=a.getMonth(),f=this.$wheelsMonth.find("ul"),g=a.getFullYear(),h=this.$wheelsYear.find("ul");for(this.sameYearOnly?(this.$wheelsMonth.addClass("full"),this.$wheelsYear.addClass("hidden")):(this.$wheelsMonth.removeClass("full"),this.$wheelsYear.removeClass("hide hidden")),f.find(".selected").removeClass("selected"),c=f.find('li[data-month="'+e+'"]'),c.addClass("selected"),f.scrollTop(f.scrollTop()+(c.position().top-f.outerHeight()/2-c.outerHeight(!0)/2)),h.empty(),b=g-10;g+11>b;b++)h.append('<li data-year="'+b+'"><button type="button">'+b+"</button></li>");d=h.find('li[data-year="'+g+'"]'),d.addClass("selected"),this.artificialScrolling=!0,h.scrollTop(h.scrollTop()+(d.position().top-h.outerHeight()/2-d.outerHeight(!0)/2)),this.artificialScrolling=!1,c.find("button").focus()},selectClicked:function(){var a=this.$wheelsMonth.find(".selected").attr("data-month"),b=this.$wheelsYear.find(".selected").attr("data-year");this.changeView("calendar",new Date(b,a,1))},setCulture:function(a){if(!a)return!1;if(!this.moment)throw c;e.locale(a)},setDate:function(a){var b=this.parseDate(a);return this.isInvalidDate(b)?(this.selectedDate=null,this.renderMonth()):this.isRestricted(b.getDate(),b.getMonth(),b.getFullYear())?(this.selectedDate=!1,this.renderMonth()):(this.selectedDate=b,this.renderMonth(b),this.$input.val(this.formatDate(b))),this.inputValue=this.$input.val(),this.selectedDate},setFormat:function(a){if(!a)return!1;if(!this.moment)throw c;this.momentFormat=a},setRestrictedDates:function(a){var b,c,d=[],e=this,f=function(a){return a===-(1/0)?{date:-(1/0),month:-(1/0),year:-(1/0)}:a===1/0?{date:1/0,month:1/0,year:1/0}:(a=e.parseDate(a),{date:a.getDate(),month:a.getMonth(),year:a.getFullYear()})};for(this.restricted=a,b=0,c=a.length;c>b;b++)d.push({from:f(a[b].from),to:f(a[b].to)});this.restrictedParsed=d},titleClicked:function(a){this.changeView("wheels",new Date(this.$headerTitle.attr("data-year"),this.$headerTitle.attr("data-month"),1))},todayClicked:function(a){var b=new Date;(b.getMonth()+""!==this.$headerTitle.attr("data-month")||b.getFullYear()+""!==this.$headerTitle.attr("data-year"))&&this.renderMonth(b)},yearClicked:function(b){this.$wheelsYear.find(".selected").removeClass("selected"),a(b.currentTarget).parent().addClass("selected")}},i.prototype.getValue=i.prototype.getDate,a.fn.datepicker=function(b){var c,d=Array.prototype.slice.call(arguments,1),e=this.each(function(){var e=a(this),f=e.data("fu.datepicker"),g="object"==typeof b&&b;f||e.data("fu.datepicker",f=new i(this,g)),"string"==typeof b&&(c=f[b].apply(f,d))});return void 0===c?e:c},a.fn.datepicker.defaults={allowPastDates:!1,date:new Date,formatDate:null,momentConfig:{culture:"en",format:"L"},parseDate:null,restricted:[],restrictedText:"Restricted",sameYearOnly:!1},a.fn.datepicker.Constructor=i,a.fn.datepicker.noConflict=function(){return a.fn.datepicker=f,this},a(document).on("mousedown.fu.datepicker.data-api","[data-initialize=datepicker]",function(b){var c=a(b.target).closest(".datepicker");c.data("datepicker")||c.datepicker(c.data())}),a(document).on("click.fu.datepicker.data-api",".datepicker .dropdown-menu",function(b){var c=a(b.target);(!c.is(".datepicker-date")||c.closest(".restricted").length)&&b.stopPropagation()}),a(document).on("click.fu.datepicker.data-api",".datepicker input",function(a){a.stopPropagation()}),a(function(){a("[data-initialize=datepicker]").each(function(){var b=a(this);b.data("datepicker")||b.datepicker(b.data())})})}(a),function(a){function b(b){a(b).css({visibility:"hidden"}),c(b)?b.parent().addClass("dropup"):b.parent().removeClass("dropup"),a(b).css({visibility:"visible"})}function c(a){var b=d(a),c={};return c.parentHeight=a.parent().outerHeight(),c.parentOffsetTop=a.parent().offset().top,c.dropdownHeight=a.outerHeight(),c.containerHeight=b.overflowElement.outerHeight(),c.containerOffsetTop=b.isWindow?b.overflowElement.scrollTop():b.overflowElement.offset().top,c.fromTop=c.parentOffsetTop-c.containerOffsetTop,c.fromBottom=c.containerHeight-c.parentHeight-(c.parentOffsetTop-c.containerOffsetTop),c.dropdownHeight<c.fromBottom?!1:c.dropdownHeight<c.fromTop?!0:c.dropdownHeight>=c.fromTop&&c.dropdownHeight>=c.fromBottom?c.fromTop>=c.fromBottom?!0:!1:void 0}function d(b){var c,d=b.attr("data-target"),e=!0;return d?"window"!==d&&(c=a(d),e=!1):a.each(b.parents(),function(b,d){return"visible"!==a(d).css("overflow")?(c=d,e=!1,!1):void 0}),e&&(c=window),{overflowElement:a(c),isWindow:e}}a(document.body).on("click.fu.dropdown-autoflip","[data-toggle=dropdown][data-flip]",function(c){"auto"===a(this).data().flip&&b(a(this).next(".dropdown-menu"))}),a(document.body).on("suggested.fu.pillbox",function(c,d){b(a(d)),a(d).parent().addClass("open")}),a.fn.dropdownautoflip=function(){}}(a),function(a){var b=a.fn.loader,c=function(b,c){this.$element=a(b),this.options=a.extend({},a.fn.loader.defaults,c),this.begin=this.$element.is("[data-begin]")?parseInt(this.$element.attr("data-begin"),10):1,this.delay=this.$element.is("[data-delay]")?parseFloat(this.$element.attr("data-delay")):150,this.end=this.$element.is("[data-end]")?parseInt(this.$element.attr("data-end"),10):8,this.frame=this.$element.is("[data-frame]")?parseInt(this.$element.attr("data-frame"),10):this.begin,this.isIElt9=!1,this.timeout={};var d=this.msieVersion();d!==!1&&9>d&&(this.$element.addClass("iefix"),this.isIElt9=!0),this.$element.attr("data-frame",this.frame+""),this.play()};c.prototype={constructor:c,destroy:function(){return this.pause(),this.$element.remove(),this.$element[0].outerHTML},ieRepaint:function(){this.isIElt9&&this.$element.addClass("iefix_repaint").removeClass("iefix_repaint")},msieVersion:function(){var a=window.navigator.userAgent,b=a.indexOf("MSIE ");return b>0?parseInt(a.substring(b+5,a.indexOf(".",b)),10):!1},next:function(){this.frame++,this.frame>this.end&&(this.frame=this.begin),this.$element.attr("data-frame",this.frame+""),this.ieRepaint()},pause:function(){clearTimeout(this.timeout)},play:function(){var a=this;clearTimeout(this.timeout),this.timeout=setTimeout(function(){a.next(),a.play()},this.delay)},previous:function(){this.frame--,this.frame<this.begin&&(this.frame=this.end),this.$element.attr("data-frame",this.frame+""),this.ieRepaint()},reset:function(){this.frame=this.begin,this.$element.attr("data-frame",this.frame+""),this.ieRepaint()}},a.fn.loader=function(b){var d,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.loader"),h="object"==typeof b&&b;g||f.data("fu.loader",g=new c(this,h)),"string"==typeof b&&(d=g[b].apply(g,e))});return void 0===d?f:d},a.fn.loader.defaults={},a.fn.loader.Constructor=c,a.fn.loader.noConflict=function(){return a.fn.loader=b,this},a(function(){a("[data-initialize=loader]").each(function(){var b=a(this);b.data("fu.loader")||b.loader(b.data())})})}(a),function(a){var b=a.fn.placard,c={accepted:"onAccept",cancelled:"onCancel"},d=function(b,c){var d=this;this.$element=a(b),this.options=a.extend({},a.fn.placard.defaults,c),"true"===this.$element.attr("data-ellipsis")&&(this.options.applyEllipsis=!0),this.$accept=this.$element.find(".placard-accept"),this.$cancel=this.$element.find(".placard-cancel"),this.$field=this.$element.find(".placard-field"),this.$footer=this.$element.find(".placard-footer"),this.$header=this.$element.find(".placard-header"),this.$popup=this.$element.find(".placard-popup"),this.actualValue=null,this.clickStamp="_",this.previousValue="",-1===this.options.revertOnCancel&&(this.options.revertOnCancel=this.$accept.length>0),this.isContentEditableDiv=this.$field.is("div"),this.isInput=this.$field.is("input"),this.divInTextareaMode=this.isContentEditableDiv&&"true"===this.$field.attr("data-textarea"),this.$field.on("focus.fu.placard",a.proxy(this.show,this)),this.$field.on("keydown.fu.placard",a.proxy(this.keyComplete,this)),this.$element.on("close.fu.placard",a.proxy(this.hide,this)),this.$accept.on("click.fu.placard",a.proxy(this.complete,this,"accepted")),this.$cancel.on("click.fu.placard",function(a){a.preventDefault(),d.complete("cancelled")}),this.applyEllipsis()},e=function(a){return a.$element.hasClass("showing")},f=function(){var b;if(b=a(document).find(".placard.showing"),b.length>0){if(b.data("fu.placard")&&b.data("fu.placard").options.explicit)return!1;b.placard("externalClickListener",{},!0)}return!0};d.prototype={constructor:d,complete:function(a){var b=this.options[c[a]],d={previousValue:this.previousValue,value:this.getValue()};b?(b(d),this.$element.trigger(a+".fu.placard",d)):("cancelled"===a&&this.options.revertOnCancel&&this.setValue(this.previousValue,!0),this.$element.trigger(a+".fu.placard",d),this.hide())},keyComplete:function(a){(this.isContentEditableDiv&&!this.divInTextareaMode||this.isInput)&&13===a.keyCode?(this.complete("accepted"),this.$field.blur()):27===a.keyCode&&(this.complete("cancelled"),this.$field.blur())},destroy:function(){return this.$element.remove(),a(document).off("click.fu.placard.externalClick."+this.clickStamp),this.$element.find("input").each(function(){a(this).attr("value",a(this).val())}),this.$element[0].outerHTML},disable:function(){this.$element.addClass("disabled"),this.$field.attr("disabled","disabled"),this.isContentEditableDiv&&this.$field.removeAttr("contenteditable"),this.hide()},applyEllipsis:function(){var a,b,c;if(this.options.applyEllipsis)if(a=this.$field.get(0),this.isContentEditableDiv&&!this.divInTextareaMode||this.isInput)a.scrollLeft=0;else if(a.scrollTop=0,a.clientHeight<a.scrollHeight){for(this.actualValue=this.getValue(),this.setValue("",!0),c="",b=0;a.clientHeight>=a.scrollHeight;)c+=this.actualValue[b],this.setValue(c+"...",!0),b++;c=c.length>0?c.substring(0,c.length-1):"",this.setValue(c+"...",!0)}},enable:function(){this.$element.removeClass("disabled"),this.$field.removeAttr("disabled"),this.isContentEditableDiv&&this.$field.attr("contenteditable","true")},externalClickListener:function(a,b){(b===!0||this.isExternalClick(a))&&this.complete(this.options.externalClickAction)},getValue:function(){return null!==this.actualValue?this.actualValue:this.isContentEditableDiv?this.$field.html():this.$field.val()},hide:function(){this.$element.hasClass("showing")&&(this.$element.removeClass("showing"),this.applyEllipsis(),a(document).off("click.fu.placard.externalClick."+this.clickStamp),this.$element.trigger("hidden.fu.placard"))},isExternalClick:function(b){var c,d,e=this.$element.get(0),f=this.options.externalClickExceptions||[],g=a(b.target);if(b.target===e||g.parents(".placard:first").get(0)===e)return!1;for(c=0,d=f.length;d>c;c++)if(g.is(f[c])||g.parents(f[c]).length>0)return!1;return!0},setValue:function(a,b){return"undefined"==typeof b&&(b=!this.options.applyEllipsis),this.isContentEditableDiv?this.$field.empty().append(a):this.$field.val(a),b||e(this)||this.applyEllipsis(),this.$field},show:function(){e(this)||f()&&(this.previousValue=this.isContentEditableDiv?this.$field.html():this.$field.val(),null!==this.actualValue&&(this.setValue(this.actualValue,!0),this.actualValue=null),this.showPlacard())},showPlacard:function(){this.$element.addClass("showing"),this.$header.length>0&&this.$popup.css("top","-"+this.$header.outerHeight(!0)+"px"),this.$footer.length>0&&this.$popup.css("bottom","-"+this.$footer.outerHeight(!0)+"px"),this.$element.trigger("shown.fu.placard"),this.clickStamp=(new Date).getTime()+(Math.floor(100*Math.random())+1),this.options.explicit||a(document).on("click.fu.placard.externalClick."+this.clickStamp,a.proxy(this.externalClickListener,this))}},a.fn.placard=function(b){var c,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.placard"),h="object"==typeof b&&b;g||f.data("fu.placard",g=new d(this,h)),"string"==typeof b&&(c=g[b].apply(g,e))});return void 0===c?f:c},a.fn.placard.defaults={onAccept:void 0,onCancel:void 0,externalClickAction:"cancelled",externalClickExceptions:[],explicit:!1,revertOnCancel:-1,applyEllipsis:!1},a.fn.placard.Constructor=d,a.fn.placard.noConflict=function(){return a.fn.placard=b,this},a(document).on("focus.fu.placard.data-api","[data-initialize=placard]",function(b){var c=a(b.target).closest(".placard");c.data("fu.placard")||c.placard(c.data())}),a(function(){a("[data-initialize=placard]").each(function(){var b=a(this);b.data("fu.placard")||b.placard(b.data())})})}(a),function(a){var b=a.fn.radio,c=function(b,c){if(this.options=a.extend({},a.fn.radio.defaults,c),"label"===b.tagName.toLowerCase()){this.$label=a(b),this.$radio=this.$label.find('input[type="radio"]'),this.groupName=this.$radio.attr("name");var d=this.$radio.attr("data-toggle");this.$toggleContainer=a(d),this.$radio.on("change",a.proxy(this.itemchecked,this)),this.setInitialState()}};c.prototype={constructor:c,setInitialState:function(){var a=this.$radio,b=(this.$label,a.prop("checked")),c=a.prop("disabled");this.setCheckedState(a,b),this.setDisabledState(a,c)},resetGroup:function(){var b=a('input[name="'+this.groupName+'"]');b.each(function(b,c){var d=a(c),e=d.parent(),f=d.attr("data-toggle"),g=a(f);e.removeClass("checked"),g.addClass("hidden")})},setCheckedState:function(b,c){var d=b,e=d.parent(),f=d.attr("data-toggle"),g=a(f);c?(this.resetGroup(),d.prop("checked",!0),e.addClass("checked"),g.removeClass("hide hidden"),e.trigger("checked.fu.radio")):(d.prop("checked",!1),e.removeClass("checked"),g.addClass("hidden"),e.trigger("unchecked.fu.radio")),e.trigger("changed.fu.radio",c)},setDisabledState:function(a,b){var c=this.$label;b?(this.$radio.prop("disabled",!0),c.addClass("disabled"),c.trigger("disabled.fu.radio")):(this.$radio.prop("disabled",!1),c.removeClass("disabled"),c.trigger("enabled.fu.radio"))},itemchecked:function(b){var c=a(b.target);this.setCheckedState(c,!0)},check:function(){this.setCheckedState(this.$radio,!0)},uncheck:function(){this.setCheckedState(this.$radio,!1)},isChecked:function(){var a=this.$radio.prop("checked");return a},enable:function(){this.setDisabledState(this.$radio,!1)},disable:function(){this.setDisabledState(this.$radio,!0)},destroy:function(){return this.$label.remove(),this.$label[0].outerHTML}},c.prototype.getValue=c.prototype.isChecked,a.fn.radio=function(b){var d,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.radio"),h="object"==typeof b&&b;g||f.data("fu.radio",g=new c(this,h)),"string"==typeof b&&(d=g[b].apply(g,e))});return void 0===d?f:d},a.fn.radio.defaults={},a.fn.radio.Constructor=c,a.fn.radio.noConflict=function(){return a.fn.radio=b,this},a(document).on("mouseover.fu.radio.data-api","[data-initialize=radio]",function(b){var c=a(b.target);c.data("fu.radio")||c.radio(c.data())}),a(function(){a("[data-initialize=radio]").each(function(){var b=a(this);b.data("fu.radio")||b.radio(b.data())})})}(a),function(a){var b=a.fn.search,c=function(b,c){this.$element=a(b),this.options=a.extend({},a.fn.search.defaults,c),"true"===this.$element.attr("data-searchOnKeyPress")&&(this.options.searchOnKeyPress=!0),this.$button=this.$element.find("button"),this.$input=this.$element.find("input"),this.$icon=this.$element.find(".glyphicon"),this.$button.on("click.fu.search",a.proxy(this.buttonclicked,this)),this.$input.on("keyup.fu.search",a.proxy(this.keypress,this)),this.activeSearch=""};c.prototype={constructor:c,destroy:function(){return this.$element.remove(),this.$element.find("input").each(function(){a(this).attr("value",a(this).val())}),this.$element[0].outerHTML},search:function(a){this.$icon.hasClass("glyphicon")&&this.$icon.removeClass("glyphicon-search").addClass("glyphicon-remove"),this.activeSearch=a,this.$element.addClass("searched"),this.$element.trigger("searched.fu.search",a)},clear:function(){this.$icon.hasClass("glyphicon")&&this.$icon.removeClass("glyphicon-remove").addClass("glyphicon-search"),this.activeSearch="",this.$input.val(""),this.$element.removeClass("searched"),this.$element.trigger("cleared.fu.search")},action:function(){var a=this.$input.val();a&&a.length>0?this.search(a):this.clear()},buttonclicked:function(b){b.preventDefault(),a(b.currentTarget).is(".disabled, :disabled")||(this.$element.hasClass("searched")?this.clear():this.action())},keypress:function(a){var b=13,c=9,d=27;a.which===b?(a.preventDefault(),this.action()):a.which===c?a.preventDefault():a.which===d?(a.preventDefault(),this.clear()):this.options.searchOnKeyPress&&this.action()},disable:function(){this.$element.addClass("disabled"),this.$input.attr("disabled","disabled"),this.$button.addClass("disabled")},enable:function(){this.$element.removeClass("disabled"),this.$input.removeAttr("disabled"),this.$button.removeClass("disabled")}},a.fn.search=function(b){
    var d,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.search"),h="object"==typeof b&&b;g||f.data("fu.search",g=new c(this,h)),"string"==typeof b&&(d=g[b].apply(g,e))});return void 0===d?f:d},a.fn.search.defaults={clearOnEmpty:!1,searchOnKeyPress:!1},a.fn.search.Constructor=c,a.fn.search.noConflict=function(){return a.fn.search=b,this},a(document).on("mousedown.fu.search.data-api","[data-initialize=search]",function(b){var c=a(b.target).closest(".search");c.data("fu.search")||c.search(c.data())}),a(function(){a("[data-initialize=search]").each(function(){var b=a(this);b.data("fu.search")||b.search(b.data())})})}(a),function(a){var b=a.fn.selectlist,c=function(b,c){this.$element=a(b),this.options=a.extend({},a.fn.selectlist.defaults,c),this.$button=this.$element.find(".btn.dropdown-toggle"),this.$hiddenField=this.$element.find(".hidden-field"),this.$label=this.$element.find(".selected-label"),this.$dropdownMenu=this.$element.find(".dropdown-menu"),this.$element.on("click.fu.selectlist",".dropdown-menu a",a.proxy(this.itemClicked,this)),this.setDefaultSelection(),("auto"===c.resize||"auto"===this.$element.attr("data-resize"))&&this.resize();var d=this.$dropdownMenu.children("li");0===d.length&&(this.disable(),this.doSelect(a(this.options.emptyLabelHTML))),this.$element.on("shown.bs.dropdown",function(){var b=a(this);a(document).on("keypress.fu.selectlist",function(c){var d=String.fromCharCode(c.which);b.find("li").each(function(b,c){return a(c).text().charAt(0).toLowerCase()===d?(a(c).children("a").focus(),!1):void 0})})}),this.$element.on("hide.bs.dropdown",function(){a(document).off("keypress.fu.selectlist")})};c.prototype={constructor:c,destroy:function(){return this.$element.remove(),this.$element[0].outerHTML},doSelect:function(b){var c;this.$selectedItem=c=b,this.$hiddenField.val(this.$selectedItem.attr("data-value")),this.$label.html(a(this.$selectedItem.children()[0]).html()),this.$element.find("li").each(function(){c.is(a(this))?a(this).attr("data-selected",!0):a(this).removeData("selected").removeAttr("data-selected")})},itemClicked:function(b){this.$element.trigger("clicked.fu.selectlist",this.$selectedItem),b.preventDefault(),a(b.currentTarget).parent("li").is(".disabled, :disabled")||(a(b.target).parent().is(this.$selectedItem)||this.itemChanged(b),this.$element.find(".dropdown-toggle").focus())},itemChanged:function(b){this.doSelect(a(b.target).closest("li"));var c=this.selectedItem();this.$element.trigger("changed.fu.selectlist",c)},resize:function(){var b=0,c=0,d=a("<div/>").addClass("selectlist-sizer");Boolean(a(document).find("html").hasClass("fuelux"))?a(document.body).append(d):a(".fuelux:first").append(d),d.append(this.$element.clone()),this.$element.find("a").each(function(){d.find(".selected-label").text(a(this).text()),c=d.find(".selectlist").outerWidth(),c+=d.find(".sr-only").outerWidth(),c>b&&(b=c)}),1>=b||(this.$button.css("width",b),this.$dropdownMenu.css("width",b),d.remove())},selectedItem:function(){var b=this.$selectedItem.text();return a.extend({text:b},this.$selectedItem.data())},selectByText:function(b){var c=a([]);this.$element.find("li").each(function(){return(this.textContent||this.innerText||a(this).text()||"").toLowerCase()===(b||"").toLowerCase()?(c=a(this),!1):void 0}),this.doSelect(c)},selectByValue:function(a){var b='li[data-value="'+a+'"]';this.selectBySelector(b)},selectByIndex:function(a){var b="li:eq("+a+")";this.selectBySelector(b)},selectBySelector:function(a){var b=this.$element.find(a);this.doSelect(b)},setDefaultSelection:function(){var a=this.$element.find("li[data-selected=true]").eq(0);0===a.length&&(a=this.$element.find("li").has("a").eq(0)),this.doSelect(a)},enable:function(){this.$element.removeClass("disabled"),this.$button.removeClass("disabled")},disable:function(){this.$element.addClass("disabled"),this.$button.addClass("disabled")}},c.prototype.getValue=c.prototype.selectedItem,a.fn.selectlist=function(b){var d,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.selectlist"),h="object"==typeof b&&b;g||f.data("fu.selectlist",g=new c(this,h)),"string"==typeof b&&(d=g[b].apply(g,e))});return void 0===d?f:d},a.fn.selectlist.defaults={emptyLabelHTML:'<li data-value=""><a href="#">No items</a></li>'},a.fn.selectlist.Constructor=c,a.fn.selectlist.noConflict=function(){return a.fn.selectlist=b,this},a(document).on("mousedown.fu.selectlist.data-api","[data-initialize=selectlist]",function(b){var c=a(b.target).closest(".selectlist");c.data("fu.selectlist")||c.selectlist(c.data())}),a(function(){a("[data-initialize=selectlist]").each(function(){var b=a(this);b.data("fu.selectlist")||b.selectlist(b.data())})})}(a),function(a){var b=a.fn.spinbox,c=function(b,c){this.$element=a(b),this.$element.find(".btn").on("click",function(a){a.preventDefault()}),this.options=a.extend({},a.fn.spinbox.defaults,c),this.options.step=this.$element.data("step")||this.options.step,this.options.value<this.options.min?this.options.value=this.options.min:this.options.max<this.options.value&&(this.options.value=this.options.max),this.$input=this.$element.find(".spinbox-input"),this.$input.on("focusout.fu.spinbox",this.$input,a.proxy(this.change,this)),this.$element.on("keydown.fu.spinbox",this.$input,a.proxy(this.keydown,this)),this.$element.on("keyup.fu.spinbox",this.$input,a.proxy(this.keyup,this)),this.bindMousewheelListeners(),this.mousewheelTimeout={},this.options.hold?(this.$element.on("mousedown.fu.spinbox",".spinbox-up",a.proxy(function(){this.startSpin(!0)},this)),this.$element.on("mouseup.fu.spinbox",".spinbox-up, .spinbox-down",a.proxy(this.stopSpin,this)),this.$element.on("mouseout.fu.spinbox",".spinbox-up, .spinbox-down",a.proxy(this.stopSpin,this)),this.$element.on("mousedown.fu.spinbox",".spinbox-down",a.proxy(function(){this.startSpin(!1)},this))):(this.$element.on("click.fu.spinbox",".spinbox-up",a.proxy(function(){this.step(!0)},this)),this.$element.on("click.fu.spinbox",".spinbox-down",a.proxy(function(){this.step(!1)},this))),this.switches={count:1,enabled:!0},"medium"===this.options.speed?this.switches.speed=300:"fast"===this.options.speed?this.switches.speed=100:this.switches.speed=500,this.options.defaultUnit=e(this.options.defaultUnit,this.options.units)?this.options.defaultUnit:"",this.unit=this.options.defaultUnit,this.lastValue=this.options.value,this.render(),this.options.disabled&&this.disable()},d=function(a,b){return Math.round(a/b)*b},e=function(b,c){var d=!1,e=b.toLowerCase();return a.each(c,function(a,b){return b=b.toLowerCase(),e===b?(d=!0,!1):void 0}),d},f=function(a){return isNaN(parseFloat(a))?a:(a>this.options.max?a=this.options.cycle?this.options.min:this.options.max:a<this.options.min&&(a=this.options.cycle?this.options.max:this.options.min),this.options.limitToStep&&this.options.step&&(a=d(a,this.options.step),a>this.options.max?a-=this.options.step:a<this.options.min&&(a+=this.options.step)),a)};c.prototype={constructor:c,destroy:function(){return this.$element.remove(),this.$element.find("input").each(function(){a(this).attr("value",a(this).val())}),this.$element[0].outerHTML},render:function(){this.setValue(this.getDisplayValue())},change:function(){this.setValue(this.getDisplayValue()),this.triggerChangedEvent()},stopSpin:function(){void 0!==this.switches.timeout&&(clearTimeout(this.switches.timeout),this.switches.count=1,this.triggerChangedEvent())},triggerChangedEvent:function(){var a=this.getValue();a!==this.lastValue&&(this.lastValue=a,this.$element.trigger("changed.fu.spinbox",a))},startSpin:function(b){if(!this.options.disabled){var c=this.switches.count;1===c?(this.step(b),c=1):c=3>c?1.5:8>c?2.5:4,this.switches.timeout=setTimeout(a.proxy(function(){this.iterate(b)},this),this.switches.speed/c),this.switches.count++}},iterate:function(a){this.step(a),this.startSpin(a)},step:function(a){this.setValue(this.getDisplayValue());var b;b=a?this.options.value+this.options.step:this.options.value-this.options.step,b=b.toFixed(5),this.setValue(b+this.unit)},getDisplayValue:function(){var a=this.parseInput(this.$input.val()),b=a?a:this.options.value;return b},setDisplayValue:function(a){this.$input.val(a)},getValue:function(){var a=this.options.value;return"."!==this.options.decimalMark&&(a=(a+"").split(".").join(this.options.decimalMark)),a+this.unit},setValue:function(a){if("."!==this.options.decimalMark&&(a=this.parseInput(a)),"number"!=typeof a){var b=a.replace(/[0-9.-]/g,"");this.unit=e(b,this.options.units)?b:this.options.defaultUnit}var c=this.getIntValue(a);return isNaN(c)&&!isFinite(c)?this.setValue(this.options.value):(c=f.call(this,c),this.options.value=c,a=c+this.unit,"."!==this.options.decimalMark&&(a=(a+"").split(".").join(this.options.decimalMark)),this.setDisplayValue(a),this)},value:function(a){return a||0===a?this.setValue(a):this.getValue()},parseInput:function(a){return a=(a+"").split(this.options.decimalMark).join(".")},getIntValue:function(a){return a="undefined"==typeof a?this.getValue():a,"undefined"!=typeof a?("string"==typeof a&&(a=this.parseInput(a)),a=parseFloat(a,10)):void 0},disable:function(){this.options.disabled=!0,this.$element.addClass("disabled"),this.$input.attr("disabled",""),this.$element.find("button").addClass("disabled")},enable:function(){this.options.disabled=!1,this.$element.removeClass("disabled"),this.$input.removeAttr("disabled"),this.$element.find("button").removeClass("disabled")},keydown:function(a){var b=a.keyCode;38===b?this.step(!0):40===b?this.step(!1):13===b&&this.change()},keyup:function(a){var b=a.keyCode;(38===b||40===b)&&this.triggerChangedEvent()},bindMousewheelListeners:function(){var b=this.$input.get(0);b.addEventListener?(b.addEventListener("mousewheel",a.proxy(this.mousewheelHandler,this),!1),b.addEventListener("DOMMouseScroll",a.proxy(this.mousewheelHandler,this),!1)):b.attachEvent("onmousewheel",a.proxy(this.mousewheelHandler,this))},mousewheelHandler:function(a){if(!this.options.disabled){var b=window.event||a,c=Math.max(-1,Math.min(1,b.wheelDelta||-b.detail)),d=this;return clearTimeout(this.mousewheelTimeout),this.mousewheelTimeout=setTimeout(function(){d.triggerChangedEvent()},300),0>c?this.step(!0):this.step(!1),b.preventDefault?b.preventDefault():b.returnValue=!1,!1}}},a.fn.spinbox=function(b){var d,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.spinbox"),h="object"==typeof b&&b;g||f.data("fu.spinbox",g=new c(this,h)),"string"==typeof b&&(d=g[b].apply(g,e))});return void 0===d?f:d},a.fn.spinbox.defaults={value:0,min:0,max:999,step:1,hold:!0,speed:"medium",disabled:!1,cycle:!1,units:[],decimalMark:".",defaultUnit:"",limitToStep:!1},a.fn.spinbox.Constructor=c,a.fn.spinbox.noConflict=function(){return a.fn.spinbox=b,this},a(document).on("mousedown.fu.spinbox.data-api","[data-initialize=spinbox]",function(b){var c=a(b.target).closest(".spinbox");c.data("fu.spinbox")||c.spinbox(c.data())}),a(function(){a("[data-initialize=spinbox]").each(function(){var b=a(this);b.data("fu.spinbox")||b.spinbox(b.data())})})}(a),function(a){function b(a,b){a.addClass("tree-selected"),"item"===a.data("type")&&b.hasClass("fueluxicon-bullet")&&b.removeClass("fueluxicon-bullet").addClass("glyphicon-ok")}function c(a,b){a.removeClass("tree-selected"),"item"===a.data("type")&&b.hasClass("glyphicon-ok")&&b.removeClass("glyphicon-ok").addClass("fueluxicon-bullet")}function d(d,e,f){a.each(f.$elements,function(b,c){var d=a(c);d[0]!==e.$element[0]&&f.dataForEvent.push(a(d).data())}),e.$element.hasClass("tree-selected")?(c(e.$element,e.$icon),f.eventType="deselected"):(b(e.$element,e.$icon),f.eventType="selected",f.dataForEvent.push(e.elementData))}function e(a,d,e){if(e.$elements[0]!==d.$element[0]){a.deselectAll(a.$element);b(d.$element,d.$icon),e.eventType="selected",e.dataForEvent=[d.elementData]}else c(d.$element,d.$icon),e.eventType="deselected",e.dataForEvent=[]}var f=a.fn.tree,g=function(b,c){this.$element=a(b),this.options=a.extend({},a.fn.tree.defaults,c),this.options.itemSelect&&this.$element.on("click.fu.tree",".tree-item",a.proxy(function(a){this.selectItem(a.currentTarget)},this)),this.$element.on("click.fu.tree",".tree-branch-name",a.proxy(function(a){this.toggleFolder(a.currentTarget)},this)),this.options.folderSelect&&(this.$element.addClass("tree-folder-select"),this.$element.off("click.fu.tree",".tree-branch-name"),this.$element.on("click.fu.tree",".icon-caret",a.proxy(function(b){this.toggleFolder(a(b.currentTarget).parent())},this)),this.$element.on("click.fu.tree",".tree-branch-name",a.proxy(function(b){this.selectFolder(a(b.currentTarget))},this))),this.render()};g.prototype={constructor:g,deselectAll:function(b){b=b||this.$element;var d=a(b).find(".tree-selected");return d.each(function(b,d){c(a(d),a(d).find(".glyphicon"))}),d},destroy:function(){return this.$element.find("li:not([data-template])").remove(),this.$element.remove(),this.$element[0].outerHTML},render:function(){this.populate(this.$element)},populate:function(b,c){var d=this,e=b.hasClass("tree")?b:b.parent(),f=e.find(".tree-loader:eq(0)"),g=e.data();c=c||!1,c===!1&&f.removeClass("hide hidden"),this.options.dataSource(g?g:{},function(c){f.addClass("hidden"),a.each(c.data,function(c,f){var g;"folder"===f.type?(g=d.$element.find("[data-template=treebranch]:eq(0)").clone().removeClass("hide hidden").removeData("template"),g.data(f),g.find(".tree-branch-name > .tree-label").html(f.text||f.name)):"item"===f.type&&(g=d.$element.find("[data-template=treeitem]:eq(0)").clone().removeClass("hide hidden").removeData("template"),g.find(".tree-item-name > .tree-label").html(f.text||f.name),g.data(f));var h=f.attr||f.dataAttributes||[];a.each(h,function(a,b){switch(a){case"cssClass":case"class":case"className":g.addClass(b);break;case"data-icon":g.find(".icon-item").removeClass().addClass("icon-item "+b),g.attr(a,b);break;case"id":g.attr(a,b),g.attr("aria-labelledby",b+"-label"),g.find(".tree-branch-name > .tree-label").attr("id",b+"-label");break;default:g.attr(a,b)}}),b.hasClass("tree-branch-header")?e.find(".tree-branch-children:eq(0)").append(g):b.append(g)}),d.$element.trigger("loaded.fu.tree",e)})},selectTreeNode:function(b,c){var f={};f.$element=a(b);var g={};g.$elements=this.$element.find(".tree-selected"),g.dataForEvent=[],"folder"===c?(f.$element=f.$element.closest(".tree-branch"),f.$icon=f.$element.find(".icon-folder")):f.$icon=f.$element.find(".icon-item"),f.elementData=f.$element.data(),this.options.multiSelect?d(this,f,g):e(this,f,g),this.$element.trigger(g.eventType+".fu.tree",{target:f.elementData,selected:g.dataForEvent}),f.$element.trigger("updated.fu.tree",{selected:g.dataForEvent,item:f.$element,eventType:g.eventType})},discloseFolder:function(b){var c=a(b),d=c.closest(".tree-branch"),e=d.find(".tree-branch-children"),f=e.eq(0);d.addClass("tree-open"),d.attr("aria-expanded","true"),f.removeClass("hide hidden"),d.find("> .tree-branch-header .icon-folder").eq(0).removeClass("glyphicon-folder-close").addClass("glyphicon-folder-open"),e.children().length||this.populate(e),this.$element.trigger("disclosedFolder.fu.tree",d.data())},closeFolder:function(b){var c=a(b),d=c.closest(".tree-branch"),e=d.find(".tree-branch-children"),f=e.eq(0);d.removeClass("tree-open"),d.attr("aria-expanded","false"),f.addClass("hidden"),d.find("> .tree-branch-header .icon-folder").eq(0).removeClass("glyphicon-folder-open").addClass("glyphicon-folder-close"),this.options.cacheItems||f.empty(),this.$element.trigger("closed.fu.tree",d.data())},toggleFolder:function(b){var c=a(b);c.find(".glyphicon-folder-close").length?this.discloseFolder(b):c.find(".glyphicon-folder-open").length&&this.closeFolder(b)},selectFolder:function(a){this.options.folderSelect&&this.selectTreeNode(a,"folder")},selectItem:function(a){this.options.itemSelect&&this.selectTreeNode(a,"item")},selectedItems:function(){var b=this.$element.find(".tree-selected"),c=[];return a.each(b,function(b,d){c.push(a(d).data())}),c},collapse:function(){var a=this,b=[],c=function d(c,e){b.push(e),0===a.$element.find(".tree-branch.tree-open:not('.hidden, .hide')").length&&(a.$element.trigger("closedAll.fu.tree",{tree:a.$element,reportedClosed:b}),a.$element.off("loaded.fu.tree",a.$element,d))};a.$element.on("closed.fu.tree",c),a.$element.find(".tree-branch.tree-open:not('.hidden, .hide')").each(function(){a.closeFolder(this)})},discloseVisible:function(){var b=this,c=b.$element.find(".tree-branch:not('.tree-open, .hidden, .hide')"),d=[],e=function f(a,e){d.push(e),d.length===c.length&&(b.$element.trigger("disclosedVisible.fu.tree",{tree:b.$element,reportedOpened:d}),b.$element.off("loaded.fu.tree",b.$element,f))};b.$element.on("loaded.fu.tree",e),b.$element.find(".tree-branch:not('.tree-open, .hidden, .hide')").each(function(){b.discloseFolder(a(this).find(".tree-branch-header"))})},discloseAll:function(){var a=this;"undefined"==typeof a.$element.data("disclosures")&&a.$element.data("disclosures",0);var b=a.options.disclosuresUpperLimit>=1&&a.$element.data("disclosures")>=a.options.disclosuresUpperLimit,c=0===a.$element.find(".tree-branch:not('.tree-open, .hidden, .hide')").length;if(c)a.$element.trigger("disclosedAll.fu.tree",{tree:a.$element,disclosures:a.$element.data("disclosures")}),a.options.cacheItems||a.$element.one("closeAll.fu.tree",function(){a.$element.data("disclosures",0)});else{if(b&&(a.$element.trigger("exceededDisclosuresLimit.fu.tree",{tree:a.$element,disclosures:a.$element.data("disclosures")}),!a.$element.data("ignore-disclosures-limit")))return;a.$element.data("disclosures",a.$element.data("disclosures")+1),a.$element.one("disclosedVisible.fu.tree",function(){a.discloseAll()}),a.discloseVisible()}},refreshFolder:function(a){var b=a.closest(".tree-branch"),c=b.find(".tree-branch-children");c.eq(0).empty(),b.hasClass("tree-open")?this.populate(c,!1):this.populate(c,!0),this.$element.trigger("refreshedFolder.fu.tree",b.data())}},g.prototype.closeAll=g.prototype.collapse,g.prototype.openFolder=g.prototype.discloseFolder,g.prototype.getValue=g.prototype.selectedItems,a.fn.tree=function(b){var c,d=Array.prototype.slice.call(arguments,1),e=this.each(function(){var e=a(this),f=e.data("fu.tree"),h="object"==typeof b&&b;f||e.data("fu.tree",f=new g(this,h)),"string"==typeof b&&(c=f[b].apply(f,d))});return void 0===c?e:c},a.fn.tree.defaults={dataSource:function(a,b){},multiSelect:!1,cacheItems:!0,folderSelect:!0,itemSelect:!0,disclosuresUpperLimit:0},a.fn.tree.Constructor=g,a.fn.tree.noConflict=function(){return a.fn.tree=f,this}}(a),function(a){var b=a.fn.wizard,c=function(b,c){var d;this.$element=a(b),this.options=a.extend({},a.fn.wizard.defaults,c),this.options.disablePreviousStep="previous"===this.$element.attr("data-restrict")?!0:this.options.disablePreviousStep,this.currentStep=this.options.selectedItem.step,this.numSteps=this.$element.find(".steps li").length,this.$prevBtn=this.$element.find("button.btn-prev"),this.$nextBtn=this.$element.find("button.btn-next"),0===this.$element.children(".steps-container").length&&(this.$element.addClass("no-steps-container"),window&&window.console&&window.console.warn&&window.console.warn('please update your wizard markup to include ".steps-container" as seen in http://getfuelux.com/javascript.html#wizard-usage-markup')),d=this.$nextBtn.children().detach(),this.nextText=a.trim(this.$nextBtn.text()),this.$nextBtn.append(d),this.$prevBtn.on("click.fu.wizard",a.proxy(this.previous,this)),this.$nextBtn.on("click.fu.wizard",a.proxy(this.next,this)),this.$element.on("click.fu.wizard","li.complete",a.proxy(this.stepclicked,this)),this.selectedItem(this.options.selectedItem),this.options.disablePreviousStep&&(this.$prevBtn.attr("disabled",!0),this.$element.find(".steps").addClass("previous-disabled"))};c.prototype={constructor:c,destroy:function(){return this.$element.remove(),this.$element[0].outerHTML},addSteps:function(b){var c,d,e,f,g,h,i=[].slice.call(arguments).slice(1),j=this.$element.find(".steps"),k=this.$element.find(".step-content");for(b=-1===b||b>this.numSteps+1?this.numSteps+1:b,i[0]instanceof Array&&(i=i[0]),g=j.find("li:nth-child("+b+")"),f=k.find(".step-pane:nth-child("+b+")"),g.length<1&&(g=null),c=0,d=i.length;d>c;c++)h=a('<li data-step="'+b+'"><span class="badge badge-info"></span></li>'),h.append(i[c].label||"").append('<span class="chevron"></span>'),h.find(".badge").append(i[c].badge||b),e=a('<div class="step-pane" data-step="'+b+'"></div>'),e.append(i[c].pane||""),g?(g.before(h),f.before(e)):(j.append(h),k.append(e)),b++;this.syncSteps(),this.numSteps=j.find("li").length,this.setState()},removeSteps:function(b,c){var d,e="nextAll",f=0,g=this.$element.find(".steps"),h=this.$element.find(".step-content");c=void 0!==c?c:1,b>g.find("li").length?d=g.find("li:last"):(d=g.find("li:nth-child("+b+")").prev(),d.length<1&&(e="children",d=g)),d[e]().each(function(){var b=a(this),d=b.attr("data-step");return c>f?(b.remove(),h.find('.step-pane[data-step="'+d+'"]:first').remove(),void f++):!1}),this.syncSteps(),this.numSteps=g.find("li").length,this.setState()},setState:function(){var b=this.currentStep>1,c=1===this.currentStep,d=this.currentStep===this.numSteps;this.options.disablePreviousStep||this.$prevBtn.attr("disabled",c===!0||b===!1);var e=this.$nextBtn.attr("data-last");if(e){this.lastText=e;var f=this.nextText;d===!0?(f=this.lastText,this.$element.addClass("complete")):this.$element.removeClass("complete");var g=this.$nextBtn.children().detach();this.$nextBtn.text(f).append(g)}var h=this.$element.find(".steps li");h.removeClass("active").removeClass("complete"),h.find("span.badge").removeClass("badge-info").removeClass("badge-success");var i=".steps li:lt("+(this.currentStep-1)+")",j=this.$element.find(i);j.addClass("complete"),j.find("span.badge").addClass("badge-success");var k=".steps li:eq("+(this.currentStep-1)+")",l=this.$element.find(k);l.addClass("active"),l.find("span.badge").addClass("badge-info");var m=this.$element.find(".step-content"),n=l.attr("data-step");m.find(".step-pane").removeClass("active"),m.find('.step-pane[data-step="'+n+'"]:first').addClass("active"),this.$element.find(".steps").first().attr("style","margin-left: 0");var o=0;this.$element.find(".steps > li").each(function(){o+=a(this).outerWidth()});var p=0;if(p=this.$element.find(".actions").length?this.$element.width()-this.$element.find(".actions").first().outerWidth():this.$element.width(),o>p){var q=o-p;this.$element.find(".steps").first().attr("style","margin-left: -"+q+"px"),this.$element.find("li.active").first().position().left<200&&(q+=this.$element.find("li.active").first().position().left-200,1>q?this.$element.find(".steps").first().attr("style","margin-left: 0"):this.$element.find(".steps").first().attr("style","margin-left: -"+q+"px"))}if("undefined"!=typeof this.initialized){var r=a.Event("changed.fu.wizard");this.$element.trigger(r,{step:this.currentStep})}this.initialized=!0},stepclicked:function(b){var c=a(b.currentTarget),d=this.$element.find(".steps li").index(c);if(!(d<this.currentStep&&this.options.disablePreviousStep)){var e=a.Event("stepclicked.fu.wizard");this.$element.trigger(e,{step:d+1}),e.isDefaultPrevented()||(this.currentStep=d+1,this.setState())}},syncSteps:function(){var b=1,c=this.$element.find(".steps"),d=this.$element.find(".step-content");c.children().each(function(){var c=a(this),e=c.find(".badge"),f=c.attr("data-step");isNaN(parseInt(e.html(),10))||e.html(b),c.attr("data-step",b),d.find('.step-pane[data-step="'+f+'"]:last').attr("data-step",b),b++})},previous:function(){if(!this.options.disablePreviousStep&&1!==this.currentStep){var b=a.Event("actionclicked.fu.wizard");if(this.$element.trigger(b,{step:this.currentStep,direction:"previous"}),!b.isDefaultPrevented()&&(this.currentStep-=1,this.setState(),this.$prevBtn.is(":focus"))){var c=this.$element.find(".active").find("input, select, textarea")[0];"undefined"!=typeof c?a(c).focus():0===this.$element.find(".active input:first").length&&this.$prevBtn.is(":disabled")&&this.$nextBtn.focus()}}},next:function(){var b=a.Event("actionclicked.fu.wizard");if(this.$element.trigger(b,{step:this.currentStep,direction:"next"}),!b.isDefaultPrevented()&&(this.currentStep<this.numSteps?(this.currentStep+=1,this.setState()):this.$element.trigger("finished.fu.wizard"),this.$nextBtn.is(":focus"))){var c=this.$element.find(".active").find("input, select, textarea")[0];"undefined"!=typeof c?a(c).focus():0===this.$element.find(".active input:first").length&&this.$nextBtn.is(":disabled")&&this.$prevBtn.focus()}},selectedItem:function(a){var b,c;return a?(c=a.step||-1,c=Number(this.$element.find('.steps li[data-name="'+c+'"]').first().attr("data-step"))||Number(c),c>=1&&c<=this.numSteps?(this.currentStep=c,this.setState()):(c=this.$element.find(".steps li.active:first").attr("data-step"),isNaN(c)||(this.currentStep=parseInt(c,10),this.setState())),b=this):(b={step:this.currentStep},this.$element.find(".steps li.active:first[data-name]").length&&(b.stepname=this.$element.find(".steps li.active:first").attr("data-name"))),b}},a.fn.wizard=function(b){var d,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.wizard"),h="object"==typeof b&&b;g||f.data("fu.wizard",g=new c(this,h)),"string"==typeof b&&(d=g[b].apply(g,e))});return void 0===d?f:d},a.fn.wizard.defaults={disablePreviousStep:!1,selectedItem:{step:-1}},a.fn.wizard.Constructor=c,a.fn.wizard.noConflict=function(){return a.fn.wizard=b,this},a(document).on("mouseover.fu.wizard.data-api","[data-initialize=wizard]",function(b){var c=a(b.target).closest(".wizard");c.data("fu.wizard")||c.wizard(c.data())}),a(function(){a("[data-initialize=wizard]").each(function(){var b=a(this);b.data("fu.wizard")||b.wizard(b.data())})})}(a),function(a){var b=a.fn.infinitescroll,c=function(b,c){this.$element=a(b),this.$element.addClass("infinitescroll"),this.options=a.extend({},a.fn.infinitescroll.defaults,c),this.curScrollTop=this.$element.scrollTop(),this.curPercentage=this.getPercentage(),this.fetchingData=!1,this.$element.on("scroll.fu.infinitescroll",a.proxy(this.onScroll,this)),this.onScroll()};c.prototype={constructor:c,destroy:function(){return this.$element.remove(),this.$element.empty(),this.$element[0].outerHTML},disable:function(){this.$element.off("scroll.fu.infinitescroll")},enable:function(){this.$element.on("scroll.fu.infinitescroll",a.proxy(this.onScroll,this))},end:function(b){var c=a('<div class="infinitescroll-end"></div>');b?c.append(b):c.append("---------"),this.$element.append(c),this.disable()},getPercentage:function(){var a="border-box"===this.$element.css("box-sizing")?this.$element.outerHeight():this.$element.height(),b=this.$element.get(0).scrollHeight;return b>a?a/(b-this.curScrollTop)*100:0},fetchData:function(b){var c,d=a('<div class="infinitescroll-load"></div>'),e=this,f=function(){var b={percentage:e.curPercentage,scrollTop:e.curScrollTop},c=a('<div class="loader"></div>');d.append(c),c.loader(),e.options.dataSource&&e.options.dataSource(b,function(a){var b;d.remove(),a.content&&e.$element.append(a.content),a.end&&(b=a.end!==!0?a.end:void 0,e.end(b)),e.fetchingData=!1})};this.fetchingData=!0,this.$element.append(d),this.options.hybrid&&b!==!0?(c=a('<button type="button" class="btn btn-primary"></button>'),"object"==typeof this.options.hybrid?c.append(this.options.hybrid.label):c.append('<span class="glyphicon glyphicon-repeat"></span>'),c.on("click.fu.infinitescroll",function(){c.remove(),f()}),d.append(c)):f()},onScroll:function(a){this.curScrollTop=this.$element.scrollTop(),this.curPercentage=this.getPercentage(),!this.fetchingData&&this.curPercentage>=this.options.percentage&&this.fetchData()}},a.fn.infinitescroll=function(b){var d,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.infinitescroll"),h="object"==typeof b&&b;g||f.data("fu.infinitescroll",g=new c(this,h)),"string"==typeof b&&(d=g[b].apply(g,e))});return void 0===d?f:d},a.fn.infinitescroll.defaults={dataSource:null,hybrid:!1,percentage:95},a.fn.infinitescroll.Constructor=c,a.fn.infinitescroll.noConflict=function(){return a.fn.infinitescroll=b,this}}(a),function(a){var b=a.fn.pillbox,c=function(b,c){this.$element=a(b),this.$moreCount=this.$element.find(".pillbox-more-count"),this.$pillGroup=this.$element.find(".pill-group"),this.$addItem=this.$element.find(".pillbox-add-item"),this.$addItemWrap=this.$addItem.parent(),this.$suggest=this.$element.find(".suggest"),this.$pillHTML='<li class="btn btn-default pill">	<span></span>	<span class="glyphicon glyphicon-close">		<span class="sr-only">Remove</span>	</span></li>',this.options=a.extend({},a.fn.pillbox.defaults,c),-1===this.options.readonly?void 0!==this.$element.attr("data-readonly")&&this.readonly(!0):this.options.readonly&&this.readonly(!0),this.acceptKeyCodes=this._generateObject(this.options.acceptKeyCodes),this.$element.on("click.fu.pillbox",".pill-group > .pill",a.proxy(this.itemClicked,this)),this.$element.on("click.fu.pillbox",a.proxy(this.inputFocus,this)),this.$element.on("keydown.fu.pillbox",".pillbox-add-item",a.proxy(this.inputEvent,this)),this.options.onKeyDown&&this.$element.on("mousedown.fu.pillbox",".suggest > li",a.proxy(this.suggestionClick,this)),this.options.edit&&(this.$element.addClass("pills-editable"),this.$element.on("blur.fu.pillbox",".pillbox-add-item",a.proxy(this.cancelEdit,this)))};c.prototype={constructor:c,destroy:function(){return this.$element.remove(),this.$element[0].outerHTML},items:function(){var b=this;return this.$pillGroup.children(".pill").map(function(){return b.getItemData(a(this))}).get()},itemClicked:function(b){var c,d=a(b.target);if(b.preventDefault(),b.stopPropagation(),this._closeSuggestions(),d.hasClass("pill"))c=d;else if(c=d.parent(),void 0===this.$element.attr("data-readonly")){if(d.hasClass("glyphicon-close"))return this.options.onRemove?this.options.onRemove(this.getItemData(c,{el:c}),a.proxy(this._removeElement,this)):this._removeElement(this.getItemData(c,{el:c})),!1;if(this.options.edit){if(c.find(".pillbox-list-edit").length)return!1;this.openEdit(c)}}this.$element.trigger("clicked.fu.pillbox",this.getItemData(c))},readonly:function(a){a?this.$element.attr("data-readonly","readonly"):this.$element.removeAttr("data-readonly"),this.options.truncate&&this.truncate(a)},suggestionClick:function(b){var c=a(b.currentTarget),d={text:c.html(),value:c.data("value")};b.preventDefault(),this.$addItem.val(""),c.data("attr")&&(d.attr=JSON.parse(c.data("attr"))),d.data=c.data("data"),this.addItems(d,!0),this._closeSuggestions()},itemCount:function(){return this.$pillGroup.children(".pill").length},addItems:function(){var b,c,d,e=this;!isFinite(String(arguments[0]))||arguments[0]instanceof Array?(b=[].slice.call(arguments).slice(0),d=b[1]&&!b[1].text):(b=[].slice.call(arguments).slice(1),c=arguments[0]),b[0]instanceof Array&&(b=b[0]),b.length&&(a.each(b,function(a,c){var d={text:c.text,value:c.value?c.value:c.text,el:e.$pillHTML};c.attr&&(d.attr=c.attr),c.data&&(d.data=c.data),b[a]=d}),this.options.edit&&this.currentEdit&&(b[0].el=this.currentEdit.wrap("<div></div>").parent().html()),d&&b.pop(1),e.options.onAdd&&d?this.options.edit&&this.currentEdit?e.options.onAdd(b[0],a.proxy(e.saveEdit,this)):e.options.onAdd(b[0],a.proxy(e.placeItems,this)):this.options.edit&&this.currentEdit?e.saveEdit(b):c?e.placeItems(c,b):e.placeItems(b,d))},removeItems:function(a,b){var c,d,e=this;if(a)for(b=b?b:1,c=0;b>c&&(d=e.$pillGroup.find("> .pill:nth-child("+a+")"),d);c++)d.remove();else this.$pillGroup.find(".pill").remove(),this._removePillTrigger({method:"removeAll"})},placeItems:function(){var b,c,d,e,f=[];!isFinite(String(arguments[0]))||arguments[0]instanceof Array?(b=[].slice.call(arguments).slice(0),e=b[1]&&!b[1].text):(b=[].slice.call(arguments).slice(1),c=arguments[0]),b[0]instanceof Array&&(b=b[0]),b.length&&(a.each(b,function(b,c){var d=a(c.el);d.attr("data-value",c.value),d.find("span:first").html(c.text),c.attr&&a.each(c.attr,function(a,b){
    "cssClass"===a||"class"===a?d.addClass(b):d.attr(a,b)}),c.data&&d.data("data",c.data),f.push(d)}),this.$pillGroup.children(".pill").length>0?c?(d=this.$pillGroup.find(".pill:nth-child("+c+")"),d.length?d.before(f):this.$pillGroup.children(".pill:last").after(f)):this.$pillGroup.children(".pill:last").after(f):this.$pillGroup.prepend(f),e&&this.$element.trigger("added.fu.pillbox",{text:b[0].text,value:b[0].value}))},inputEvent:function(a){var b,c,d,e,f=this,g=this.$addItem.val();if(this.acceptKeyCodes[a.keyCode])return this.options.onKeyDown&&this._isSuggestionsOpen()&&(e=this.$suggest.find(".pillbox-suggest-sel"),e.length&&(g=e.html(),b=e.data("value"),c=e.data("attr"))),(g.replace(/[ ]*\,[ ]*/,"").match(/\S/)||this.options.allowEmptyPills&&g.length)&&(this._closeSuggestions(),this.$addItem.hide(),c?this.addItems({text:g,value:b,attr:JSON.parse(c)},!0):this.addItems({text:g,value:b},!0),setTimeout(function(){f.$addItem.show().val("").attr({size:10})},0)),a.preventDefault(),!0;if(8===a.keyCode||46===a.keyCode){if(!g.length)return a.preventDefault(),this.options.edit&&this.currentEdit?(this.cancelEdit(),!0):(this._closeSuggestions(),d=this.$pillGroup.children(".pill:last"),d.hasClass("pillbox-highlight")?this._removeElement(this.getItemData(d,{el:d})):d.addClass("pillbox-highlight"),!0)}else g.length>10&&this.$addItem.width()<this.$pillGroup.width()-6&&this.$addItem.attr({size:g.length+3});if(this.$pillGroup.find(".pill").removeClass("pillbox-highlight"),this.options.onKeyDown){if(9===a.keyCode||38===a.keyCode||40===a.keyCode)return this._isSuggestionsOpen()&&this._keySuggestions(a),!0;this.callbackId=a.timeStamp,this.options.onKeyDown({event:a,value:g},function(b){f._openSuggestions(a,b)})}},openEdit:function(a){var b=a.index()+1,c=this.$addItemWrap.detach().hide();this.$pillGroup.find(".pill:nth-child("+b+")").before(c),this.currentEdit=a.detach(),c.addClass("editing"),this.$addItem.val(a.find("span:first").html()),c.show(),this.$addItem.focus().select()},cancelEdit:function(a){var b;return this.currentEdit?(this._closeSuggestions(),a&&this.$addItemWrap.before(this.currentEdit),this.currentEdit=!1,b=this.$addItemWrap.detach(),b.removeClass("editing"),this.$addItem.val(""),void this.$pillGroup.append(b)):!1},saveEdit:function(){var b=arguments[0][0]?arguments[0][0]:arguments[0];this.currentEdit=a(b.el),this.currentEdit.data("value",b.value),this.currentEdit.find("span:first").html(b.text),this.$addItemWrap.hide(),this.$addItemWrap.before(this.currentEdit),this.currentEdit=!1,this.$addItem.val(""),this.$addItemWrap.removeClass("editing"),this.$pillGroup.append(this.$addItemWrap.detach().show()),this.$element.trigger("edited.fu.pillbox",{value:b.value,text:b.text})},removeBySelector:function(){var b=[].slice.call(arguments).slice(0),c=this;a.each(b,function(a,b){c.$pillGroup.find(b).remove()}),this._removePillTrigger({method:"removeBySelector",removedSelectors:b})},removeByValue:function(){var b=[].slice.call(arguments).slice(0),c=this;a.each(b,function(a,b){c.$pillGroup.find('> .pill[data-value="'+b+'"]').remove()}),this._removePillTrigger({method:"removeByValue",removedValues:b})},removeByText:function(){var b=[].slice.call(arguments).slice(0),c=this;a.each(b,function(a,b){c.$pillGroup.find('> .pill:contains("'+b+'")').remove()}),this._removePillTrigger({method:"removeByText",removedText:b})},truncate:function(b){var c,d,e,f,g,h=this;this.$element.removeClass("truncate"),this.$addItemWrap.removeClass("truncated"),this.$pillGroup.find(".pill").removeClass("truncated"),b&&(this.$element.addClass("truncate"),c=this.$element.width(),d=!1,e=0,f=this.$pillGroup.find(".pill").length,g=0,this.$pillGroup.find(".pill").each(function(){var b=a(this);d?b.addClass("truncated"):(e++,h.$moreCount.text(f-e),g+b.outerWidth(!0)+h.$addItemWrap.outerWidth(!0)<=c?g+=b.outerWidth(!0):(h.$moreCount.text(f-e+1),b.addClass("truncated"),d=!0))}),e===f&&this.$addItemWrap.addClass("truncated"))},inputFocus:function(a){this.$element.find(".pillbox-add-item").focus()},getItemData:function(b,c){return a.extend({text:b.find("span:first").html()},b.data(),c)},_removeElement:function(a){a.el.remove(),delete a.el,this.$element.trigger("removed.fu.pillbox",a)},_removePillTrigger:function(a){this.$element.trigger("removed.fu.pillbox",a)},_generateObject:function(b){var c={};return a.each(b,function(a,b){c[b]=!0}),c},_openSuggestions:function(b,c){var d=a("<ul>");return this.callbackId!==b.timeStamp?!1:void(c.data&&c.data.length&&(a.each(c.data,function(b,c){var e=c.value?c.value:c.text,f=a('<li data-value="'+e+'">'+c.text+"</li>");c.attr&&f.data("attr",JSON.stringify(c.attr)),c.data&&f.data("data",c.data),d.append(f)}),this.$suggest.html("").append(d.children()),a(document.body).trigger("suggested.fu.pillbox",this.$suggest)))},_closeSuggestions:function(){this.$suggest.html("").parent().removeClass("open")},_isSuggestionsOpen:function(){return this.$suggest.parent().hasClass("open")},_keySuggestions:function(a){var b,c=this.$suggest.find("li.pillbox-suggest-sel"),d=38===a.keyCode;a.preventDefault(),c.length?(b=d?c.prev():c.next(),b.length||(b=d?this.$suggest.find("li:last"):this.$suggest.find("li:first")),b&&(b.addClass("pillbox-suggest-sel"),c.removeClass("pillbox-suggest-sel"))):(c=this.$suggest.find("li:first"),c.addClass("pillbox-suggest-sel"))}},c.prototype.getValue=c.prototype.items,a.fn.pillbox=function(b){var d,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.pillbox"),h="object"==typeof b&&b;g||f.data("fu.pillbox",g=new c(this,h)),"string"==typeof b&&(d=g[b].apply(g,e))});return void 0===d?f:d},a.fn.pillbox.defaults={onAdd:void 0,onRemove:void 0,onKeyDown:void 0,edit:!1,readonly:-1,truncate:!1,acceptKeyCodes:[13,188],allowEmptyPills:!1},a.fn.pillbox.Constructor=c,a.fn.pillbox.noConflict=function(){return a.fn.pillbox=b,this},a(document).on("mousedown.fu.pillbox.data-api","[data-initialize=pillbox]",function(b){var c=a(b.target).closest(".pillbox");c.data("fu.pillbox")||c.pillbox(c.data())}),a(function(){a("[data-initialize=pillbox]").each(function(){var b=a(this);b.data("fu.pillbox")||b.pillbox(b.data())})})}(a),function(a){var b=a.fn.repeater,c=function(b,c){var d,e,f=this;this.$element=a(b),this.$canvas=this.$element.find(".repeater-canvas"),this.$count=this.$element.find(".repeater-count"),this.$end=this.$element.find(".repeater-end"),this.$filters=this.$element.find(".repeater-filters"),this.$loader=this.$element.find(".repeater-loader"),this.$pageSize=this.$element.find(".repeater-itemization .selectlist"),this.$nextBtn=this.$element.find(".repeater-next"),this.$pages=this.$element.find(".repeater-pages"),this.$prevBtn=this.$element.find(".repeater-prev"),this.$primaryPaging=this.$element.find(".repeater-primaryPaging"),this.$search=this.$element.find(".repeater-search").find(".search"),this.$secondaryPaging=this.$element.find(".repeater-secondaryPaging"),this.$start=this.$element.find(".repeater-start"),this.$viewport=this.$element.find(".repeater-viewport"),this.$views=this.$element.find(".repeater-views"),this.currentPage=0,this.currentView=null,this.isDisabled=!1,this.infiniteScrollingCallback=function(){},this.infiniteScrollingCont=null,this.infiniteScrollingEnabled=!1,this.infiniteScrollingEnd=null,this.infiniteScrollingOptions={},this.lastPageInput=0,this.options=a.extend({},a.fn.repeater.defaults,c),this.pageIncrement=0,this.resizeTimeout={},this.stamp=(new Date).getTime()+(Math.floor(100*Math.random())+1),this.storedDataSourceOpts=null,this.syncingViewButtonState=!1,this.viewOptions={},this.viewType=null,this.$filters.selectlist(),this.$pageSize.selectlist(),this.$primaryPaging.find(".combobox").combobox(),this.$search.search({searchOnKeyPress:this.options.searchOnKeyPress}),this.$filters.on("changed.fu.selectlist",function(a,b){f.$element.trigger("filtered.fu.repeater",b),f.render({clearInfinite:!0,pageIncrement:null})}),this.$nextBtn.on("click.fu.repeater",a.proxy(this.next,this)),this.$pageSize.on("changed.fu.selectlist",function(a,b){f.$element.trigger("pageSizeChanged.fu.repeater",b),f.render({pageIncrement:null})}),this.$prevBtn.on("click.fu.repeater",a.proxy(this.previous,this)),this.$primaryPaging.find(".combobox").on("changed.fu.combobox",function(a,b){f.$element.trigger("pageChanged.fu.repeater",[b.text,b]),f.pageInputChange(b.text)}),this.$search.on("searched.fu.search cleared.fu.search",function(a,b){f.$element.trigger("searchChanged.fu.repeater",b),f.render({clearInfinite:!0,pageIncrement:null})}),this.$secondaryPaging.on("blur.fu.repeater",function(a){f.pageInputChange(f.$secondaryPaging.val())}),this.$secondaryPaging.on("keyup",function(a){13===a.keyCode&&f.pageInputChange(f.$secondaryPaging.val())}),this.$views.find("input").on("change.fu.repeater",a.proxy(this.viewChanged,this)),a(window).on("resize.fu.repeater."+this.stamp,function(a){clearTimeout(f.resizeTimeout),f.resizeTimeout=setTimeout(function(){f.resize(),f.$element.trigger("resized.fu.repeater")},75)}),this.$loader.loader(),this.$loader.loader("pause"),-1!==this.options.defaultView?e=this.options.defaultView:(d=this.$views.find("label.active input"),e=d.length>0?d.val():"list"),this.setViewOptions(e),this.initViewTypes(function(){f.resize(),f.$element.trigger("resized.fu.repeater"),f.render({changeView:e})})};c.prototype={constructor:c,clear:function(b){function c(b){var d=[];b.children().each(function(){var b=a(this),e=b.attr("data-preserve");"deep"===e?(b.detach(),d.push(b)):"shallow"===e&&(c(b),b.detach(),d.push(b))}),b.empty(),b.append(d)}var d,e;b=b||{},b.preserve?(!this.infiniteScrollingEnabled||b.clearInfinite)&&c(this.$canvas):this.$canvas.empty(),d=void 0!==b.viewChanged?b.viewChanged:!1,e=a.fn.repeater.viewTypes[this.viewType]||{},!d&&e.cleared&&e.cleared.call(this,{options:b})},clearPreservedDataSourceOptions:function(){this.storedDataSourceOpts=null},destroy:function(){var b;return this.$element.find("input").each(function(){a(this).attr("value",a(this).val())}),this.$canvas.empty(),b=this.$element[0].outerHTML,this.$element.find(".combobox").combobox("destroy"),this.$element.find(".selectlist").selectlist("destroy"),this.$element.find(".search").search("destroy"),this.infiniteScrollingEnabled&&a(this.infiniteScrollingCont).infinitescroll("destroy"),this.$element.remove(),a(window).off("resize.fu.repeater."+this.stamp),b},disable:function(){var b="disable",c="disabled",d=a.fn.repeater.viewTypes[this.viewType]||{};this.$search.search(b),this.$filters.selectlist(b),this.$views.find("label, input").addClass(c).attr(c,c),this.$pageSize.selectlist(b),this.$primaryPaging.find(".combobox").combobox(b),this.$secondaryPaging.attr(c,c),this.$prevBtn.attr(c,c),this.$nextBtn.attr(c,c),d.enabled&&d.enabled.call(this,{status:!1}),this.isDisabled=!0,this.$element.addClass("disabled"),this.$element.trigger("disabled.fu.repeater")},enable:function(){var b="disabled",c="enable",d="page-end",e=a.fn.repeater.viewTypes[this.viewType]||{};this.$search.search(c),this.$filters.selectlist(c),this.$views.find("label, input").removeClass(b).removeAttr(b),this.$pageSize.selectlist("enable"),this.$primaryPaging.find(".combobox").combobox(c),this.$secondaryPaging.removeAttr(b),this.$prevBtn.hasClass(d)||this.$prevBtn.removeAttr(b),this.$nextBtn.hasClass(d)||this.$nextBtn.removeAttr(b),this.$prevBtn.hasClass(d)&&this.$nextBtn.hasClass(d)&&this.$primaryPaging.combobox("disable"),0!==parseInt(this.$count.html())?this.$pageSize.selectlist("enable"):this.$pageSize.selectlist("disable"),e.enabled&&e.enabled.call(this,{status:!0}),this.isDisabled=!1,this.$element.removeClass("disabled"),this.$element.trigger("enabled.fu.repeater")},getDataOptions:function(b){var c,d,e={},f={};return b=b||{},f.filter=this.$filters.length>0?this.$filters.selectlist("selectedItem"):{text:"All",value:"all"},f.view=this.currentView,this.infiniteScrollingEnabled||(f.pageSize=this.$pageSize.length>0?parseInt(this.$pageSize.selectlist("selectedItem").value,10):25),void 0!==b.pageIncrement&&(null===b.pageIncrement?this.currentPage=0:this.currentPage+=b.pageIncrement),f.pageIndex=this.currentPage,c=this.$search.length>0?this.$search.find("input").val():"",""!==c&&(f.search=c),b.dataSourceOptions&&(e=b.dataSourceOptions,b.preserveDataSourceOptions&&(this.storedDataSourceOpts=this.storedDataSourceOpts?a.extend(this.storedDataSourceOpts,e):e)),this.storedDataSourceOpts&&(e=a.extend(this.storedDataSourceOpts,e)),d=a.fn.repeater.viewTypes[this.viewType]||{},d=d.dataOptions,d?(d=d.call(this,f),f=a.extend(d,e)):f=a.extend(f,e),f},infiniteScrolling:function(a,b){var c,d,e=this.$element.find(".repeater-footer"),f=this.$element.find(".repeater-viewport");b=b||{},a?(this.infiniteScrollingEnabled=!0,this.infiniteScrollingEnd=b.end,delete b.dataSource,delete b.end,this.infiniteScrollingOptions=b,f.css({height:f.height()+e.outerHeight()}),e.hide()):(c=this.infiniteScrollingCont,d=c.data(),delete d.infinitescroll,c.off("scroll"),c.removeClass("infinitescroll"),this.infiniteScrollingCont=null,this.infiniteScrollingEnabled=!1,this.infiniteScrollingEnd=null,this.infiniteScrollingOptions={},f.css({height:f.height()-e.outerHeight()}),e.show())},infiniteScrollPaging:function(a,b){var c=this.infiniteScrollingEnd!==!0?this.infiniteScrollingEnd:void 0,d=a.page,e=a.pages;this.currentPage=void 0!==d?d:NaN,(a.end===!0||this.currentPage+1>=e)&&this.infiniteScrollingCont.infinitescroll("end",c)},initInfiniteScrolling:function(){var b,c,d=this.$canvas.find('[data-infinite="true"]:first');d=d.length<1?this.$canvas:d,d.data("fu.infinitescroll")?d.infinitescroll("enable"):(c=this,b=a.extend({},this.infiniteScrollingOptions),b.dataSource=function(a,b){c.infiniteScrollingCallback=b,c.render({pageIncrement:1})},d.infinitescroll(b),this.infiniteScrollingCont=d)},initViewTypes:function(b){function c(a){function d(){a++,e>a?c(a):b()}g[a].initialize?g[a].initialize.call(f,{},function(){d()}):d()}var d,e,f=this,g=[];for(d in a.fn.repeater.viewTypes)g.push(a.fn.repeater.viewTypes[d]);e=g.length,e>0?c(0):b()},itemization:function(a){this.$count.html(void 0!==a.count?a.count:"?"),this.$end.html(void 0!==a.end?a.end:"?"),this.$start.html(void 0!==a.start?a.start:"?")},next:function(a){var b="disabled";this.$nextBtn.attr(b,b),this.$prevBtn.attr(b,b),this.pageIncrement=1,this.$element.trigger("nextClicked.fu.repeater"),this.render({pageIncrement:this.pageIncrement})},pageInputChange:function(a){var b;a!==this.lastPageInput&&(this.lastPageInput=a,a=parseInt(a,10)-1,b=a-this.currentPage,this.$element.trigger("pageChanged.fu.repeater",a),this.render({pageIncrement:b}))},pagination:function(a){var b,c,d,e,f="active",g="disabled",h=a.page,i="page-end",j=a.pages;if(this.currentPage=void 0!==h?h:NaN,this.$primaryPaging.removeClass(f),this.$secondaryPaging.removeClass(f),e=0===j?0:this.currentPage+1,j<=this.viewOptions.dropPagingCap){for(this.$primaryPaging.addClass(f),b=this.$primaryPaging.find(".dropdown-menu"),b.empty(),c=0;j>c;c++)d=c+1,b.append('<li data-value="'+d+'"><a href="#">'+d+"</a></li>");this.$primaryPaging.find("input.form-control").val(e)}else this.$secondaryPaging.addClass(f),this.$secondaryPaging.val(e);this.lastPageInput=this.currentPage+1+"",this.$pages.html(""+j),this.currentPage+1<j?(this.$nextBtn.removeAttr(g),this.$nextBtn.removeClass(i)):(this.$nextBtn.attr(g,g),this.$nextBtn.addClass(i)),this.currentPage-1>=0?(this.$prevBtn.removeAttr(g),this.$prevBtn.removeClass(i)):(this.$prevBtn.attr(g,g),this.$prevBtn.addClass(i)),0!==this.pageIncrement&&(this.pageIncrement>0?this.$nextBtn.is(":disabled")?this.$prevBtn.focus():this.$nextBtn.focus():this.$prevBtn.is(":disabled")?this.$nextBtn.focus():this.$prevBtn.focus())},previous:function(){var a="disabled";this.$nextBtn.attr(a,a),this.$prevBtn.attr(a,a),this.pageIncrement=-1,this.$element.trigger("previousClicked.fu.repeater"),this.render({pageIncrement:this.pageIncrement})},render:function(b){var c,d,e=this,f=!1,g=a.fn.repeater.viewTypes[this.viewType]||{};b=b||{},this.disable(),b.changeView&&this.currentView!==b.changeView&&(d=this.currentView,this.currentView=b.changeView,this.viewType=this.currentView.split(".")[0],this.setViewOptions(this.currentView),this.$element.attr("data-currentview",this.currentView),this.$element.attr("data-viewtype",this.viewType),f=!0,b.viewChanged=f,this.$element.trigger("viewChanged.fu.repeater",this.currentView),this.infiniteScrollingEnabled&&e.infiniteScrolling(!1),g=a.fn.repeater.viewTypes[this.viewType]||{},g.selected&&g.selected.call(this,{prevView:d})),this.syncViewButtonState(),b.preserve=void 0!==b.preserve?b.preserve:!f,this.clear(b),(!this.infiniteScrollingEnabled||this.infiniteScrollingEnabled&&f)&&this.$loader.show().loader("play"),c=this.getDataOptions(b),this.viewOptions.dataSource(c,function(a){a=a||{},e.infiniteScrollingEnabled?e.infiniteScrollingCallback({}):(e.itemization(a),e.pagination(a)),e.runRenderer(g,a,function(){e.infiniteScrollingEnabled&&((f||b.clearInfinite)&&e.initInfiniteScrolling(),e.infiniteScrollPaging(a,b)),e.$loader.hide().loader("pause"),e.enable(),e.$element.trigger("rendered.fu.repeater",{data:a,options:c,renderOptions:b}),e.$element.trigger("loaded.fu.repeater",c)})})},resize:function(){var b,c,d=-1===this.viewOptions.staticHeight?this.$element.attr("data-staticheight"):this.viewOptions.staticHeight,e={};if(this.viewType&&(e=a.fn.repeater.viewTypes[this.viewType]||{}),void 0!==d&&d!==!1&&"false"!==d){this.$canvas.addClass("scrolling"),c={bottom:this.$viewport.css("margin-bottom"),top:this.$viewport.css("margin-top")};var f="true"===d||d===!0?this.$element.height():parseInt(d,10),g=this.$element.find(".repeater-header").outerHeight(),h=this.$element.find(".repeater-footer").outerHeight(),i="auto"===c.bottom?0:parseInt(c.bottom,10),j="auto"===c.top?0:parseInt(c.top,10);b=f-g-h-i-j,this.$viewport.outerHeight(b)}else this.$canvas.removeClass("scrolling");e.resize&&e.resize.call(this,{height:this.$element.outerHeight(),width:this.$element.outerWidth()})},runRenderer:function(b,c,d){function e(b,c){var d;c&&(d=c.action?c.action:"append","none"!==d&&void 0!==c.item&&(b=void 0!==c.container?a(c.container):b,b[d](c.item)))}var f,g,h,i,j,k;if(b.render)b.render.call(this,{container:this.$canvas,data:c},function(){d()});else{if(b.before&&(i=b.before.call(this,{container:this.$canvas,data:c}),e(this.$canvas,i)),f=this.$canvas.find('[data-container="true"]:last'),f=f.length>0?f:this.$canvas,b.renderItem){for(j=b.repeat||"data.items",j=j.split("."),"data"===j[0]||"this"===j[0]?(k="this"===j[0]?this:c,j.shift()):(j=[],k=[],window.console&&window.console.warn&&window.console.warn('WARNING: Repeater plugin "repeat" value must start with either "data" or "this"')),g=0,h=j.length;h>g;g++){if(void 0===k[j[g]]){k=[],window.console&&window.console.warn&&window.console.warn("WARNING: Repeater unable to find property to iterate renderItem on.");break}k=k[j[g]]}for(g=0,h=k.length;h>g;g++)i=b.renderItem.call(this,{container:f,data:c,index:g,subset:k}),e(f,i)}b.after&&(i=b.after.call(this,{container:this.$canvas,data:c}),e(this.$canvas,i)),d()}},setViewOptions:function(b){var c={},d=b.split(".")[1];c=this.options.views?this.options.views[d]||this.options.views[b]||{}:{},this.viewOptions=a.extend({},this.options,c)},viewChanged:function(b){var c=a(b.target),d=c.val();this.syncingViewButtonState||(this.isDisabled||c.parents("label:first").hasClass("disabled")?this.syncViewButtonState():this.render({changeView:d,pageIncrement:null}))},syncViewButtonState:function(){var a=this.$views.find('input[value="'+this.currentView+'"]');this.syncingViewButtonState=!0,this.$views.find("input").prop("checked",!1),this.$views.find("label.active").removeClass("active"),a.length>0&&(a.prop("checked",!0),a.parents("label:first").addClass("active")),this.syncingViewButtonState=!1}},a.fn.repeater=function(b){var d,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.repeater"),h="object"==typeof b&&b;g||f.data("fu.repeater",g=new c(this,h)),"string"==typeof b&&(d=g[b].apply(g,e))});return void 0===d?f:d},a.fn.repeater.defaults={dataSource:function(a,b){b({count:0,end:0,items:[],page:0,pages:1,start:0})},defaultView:-1,dropPagingCap:10,staticHeight:-1,views:null,searchOnKeyPress:!1},a.fn.repeater.viewTypes={},a.fn.repeater.Constructor=c,a.fn.repeater.noConflict=function(){return a.fn.repeater=b,this}}(a),function(a){function b(a,b){if(!b)return!1;if(!a||b.length!==a.length)return!0;for(var c=0;c<b.length;c++){if(!a[c])return!0;for(var d in b[c])if(a[c][d]!==b[c][d])return!0}return!1}function c(b,c,d,e,f){var g=e[f].className,h=c[d][e[f].property],i=a("<td></td>"),j=e[f]._auto_width,k=e[f].property;if(this.viewOptions.list_actions!==!1&&"@_ACTIONS_@"===k&&(h='<div class="repeater-list-actions-placeholder" style="width: '+this.list_actions_width+'px"></div>'),h=void 0!==h?h:"",i.addClass(void 0!==g?g:"").append(h),void 0!==j&&i.outerWidth(j),b.append(i),"multi"===this.viewOptions.list_selectable&&"@_CHECKBOX_@"===e[f].property){var l='<label data-row="'+d+'" class="checkbox-custom checkbox-inline body-checkbox repeater-select-checkbox"><input class="sr-only" type="checkbox"></label>';i.html(l)}"@_CHECKBOX_@"!==e[f].property&&"@_ACTIONS_@"!==e[f].property&&this.viewOptions.list_columnRendered&&this.viewOptions.list_columnRendered({container:b,columnAttr:e[f].property,item:i,rowData:c[d]},function(){})}function d(b,c,d){var e,f,g,h,i,j="glyphicon-chevron-down",k=".glyphicon.rlc:first",l="glyphicon-chevron-up",m=a('<div class="repeater-list-heading"><span class="glyphicon rlc"></span></div>'),n='<div class="repeater-list-heading header-checkbox"><label class="checkbox-custom checkbox-inline repeater-select-checkbox"><input class="sr-only" type="checkbox"></label><div class="clearfix"></div></div>',o=a("<th></th>"),p=this;if(m.data("fu_item_index",d),m.prepend(c[d].label),o.html(m.html()).find("[id]").removeAttr("id"),"@_CHECKBOX_@"!==c[d].property?o.append(m):o.append(n),e=o.add(m),h=m.find(k),i=h.add(o.find(k)),this.viewOptions.list_actions&&"@_ACTIONS_@"===c[d].property){var q=this.list_actions_width;o.css("width",q),m.css("width",q)}f=c[d].className,void 0!==f&&e.addClass(f),g=c[d].sortable,g&&(e.addClass("sortable"),m.on("click.fu.repeaterList",function(){p.isDisabled||(p.list_sortProperty="string"==typeof g?g:c[d].property,m.hasClass("sorted")?h.hasClass(l)?(i.removeClass(l).addClass(j),p.list_sortDirection="desc"):p.viewOptions.list_sortClearing?(e.removeClass("sorted"),i.removeClass(j),p.list_sortDirection=null,p.list_sortProperty=null):(i.removeClass(j).addClass(l),p.list_sortDirection="asc"):(b.find("th, .repeater-list-heading").removeClass("sorted"),i.removeClass(j).addClass(l),p.list_sortDirection="asc",e.addClass("sorted")),p.render({clearInfinite:!0,pageIncrement:null}))})),("asc"===c[d].sortDirection||"desc"===c[d].sortDirection)&&(b.find("th, .repeater-list-heading").removeClass("sorted"),e.addClass("sortable sorted"),"asc"===c[d].sortDirection?(i.addClass(l),this.list_sortDirection="asc"):(i.addClass(j),this.list_sortDirection="desc"),this.list_sortProperty="string"==typeof g?g:c[d].property),b.append(o)}function e(b,d,e){var f,g,h=a("<tr></tr>"),i=this,k="multi"===this.viewOptions.list_selectable,l=this.viewOptions.list_actions;for(this.viewOptions.list_selectable&&(h.addClass("selectable"),h.attr("tabindex",0),h.data("item_data",d[e]),h.on("click.fu.repeaterList",function(){if(!i.isDisabled){var b=a(this),c=a(this).index();c+=1;var d=i.$element.find(".frozen-column-wrapper tr:nth-child("+c+")"),e=i.$element.find(".actions-column-wrapper tr:nth-child("+c+")"),f=i.$element.find(".frozen-column-wrapper tr:nth-child("+c+") .checkbox-inline");b.is(".selected")?(b.removeClass("selected"),k?(f.checkbox("uncheck"),d.removeClass("selected"),l&&e.removeClass("selected")):b.find(".repeater-list-check").remove(),i.$element.trigger("deselected.fu.repeaterList",b)):(k?(f.checkbox("check"),b.addClass("selected"),d.addClass("selected"),l&&e.addClass("selected")):(i.$canvas.find(".repeater-list-check").remove(),i.$canvas.find(".repeater-list tbody tr.selected").each(function(){a(this).removeClass("selected"),i.$element.trigger("deselected.fu.repeaterList",a(this))}),b.find("td:first").prepend('<div class="repeater-list-check"><span class="glyphicon glyphicon-ok"></span></div>'),b.addClass("selected"),d.addClass("selected")),i.$element.trigger("selected.fu.repeaterList",b)),j.call(i)}}),h.keyup(function(a){13===a.keyCode&&h.trigger("click.fu.repeaterList")})),this.viewOptions.list_actions&&!this.viewOptions.list_selectable&&h.data("item_data",d[e]),b.append(h),f=0,g=this.list_columns.length;g>f;f++)c.call(this,h,d,e,this.list_columns,f);this.viewOptions.list_rowRendered&&this.viewOptions.list_rowRendered({container:b,item:h,rowData:d[e]},function(){})}function f(b,c){var d,e=b.find("tbody");e.length<1&&(e=a('<tbody data-container="true"></tbody>'),b.append(e)),"string"==typeof c.error&&c.error.length>0?(d=a('<tr class="empty text-danger"><td colspan="'+this.list_columns.length+'"></td></tr>'),d.find("td").append(c.error),e.append(d)):c.items&&c.items.length<1&&(d=a('<tr class="empty"><td colspan="'+this.list_columns.length+'"></td></tr>'),d.find("td").append(this.viewOptions.list_noItemsHTML),e.append(d))}function g(c,e){var f,g,i,j=e.columns||[],k=c.find("thead");if(this.list_firstRender||b(this.list_columns,j)||0===k.length){if(k.remove(),e.count<1&&(this.list_noItems=!0),"multi"===this.viewOptions.list_selectable&&!this.list_noItems){var l={label:"c",property:"@_CHECKBOX_@",sortable:!1};j.splice(0,0,l)}if(this.list_columns=j,this.list_firstRender=!1,this.$loader.removeClass("noHeader"),this.viewOptions.list_actions&&!this.list_noItems){var m={label:this.viewOptions.list_actions.label||'<span class="actions-hidden">a</span>',property:"@_ACTIONS_@",sortable:!1,width:this.list_actions_width};j.push(m)}for(k=a('<thead data-preserve="deep"><tr></tr></thead>'),i=k.find("tr"),f=0,g=j.length;g>f;f++)d.call(this,i,j,f);if(c.prepend(k),"multi"===this.viewOptions.list_selectable&&!this.list_noItems){var n=this.$element.find(".repeater-list-wrapper .header-checkbox").outerWidth(),o=a.grep(j,function(a){return"@_CHECKBOX_@"===a.property})[0];o.width=n}h.call(this,i)}}function h(b){var c,d,e,f,g=[],h=this;if(this.viewOptions.list_columnSizing&&(c=0,f=0,b.find("th").each(function(){var b,d=a(this);if(void 0!==h.list_columns[c].width)b=h.list_columns[c].width,d.outerWidth(b),f+=d.outerWidth(),h.list_columns[c]._auto_width=b;else{var e=d.find(".repeater-list-heading").outerWidth();g.push({col:d,index:c,minWidth:e})}c++}),d=g.length,d>0)){var i=this.$canvas.find(".repeater-list-wrapper").outerWidth();for(e=Math.floor((i-f)/d),c=0;d>c;c++)g[c].minWidth>e&&(e=g[c].minWidth),g[c].col.outerWidth(e),this.list_columns[g[c].index]._auto_width=e}}function i(){var a=window.navigator.userAgent,b=a.indexOf("MSIE "),c=a.indexOf("Firefox");return b>0?"ie-"+parseInt(a.substring(b+5,a.indexOf(".",b))):c>0?"firefox":""}function j(){var a=this.$canvas.find(".repeater-list-wrapper > table .selected"),b=this.$element.find(".table-actions");a.length>0?b.find("thead .btn").removeAttr("disabled"):b.find("thead .btn").attr("disabled","disabled")}a.fn.repeater&&(a.fn.repeater.Constructor.prototype.list_clearSelectedItems=function(){this.$canvas.find(".repeater-list-check").remove(),this.$canvas.find(".repeater-list table tbody tr.selected").removeClass("selected")},a.fn.repeater.Constructor.prototype.list_highlightColumn=function(b,c){var d=this.$canvas.find(".repeater-list-wrapper > table tbody");(this.viewOptions.list_highlightSortedColumn||c)&&(d.find("td.sorted").removeClass("sorted"),d.find("tr").each(function(){var c=a(this).find("td:nth-child("+(b+1)+")").filter(function(){return!a(this).parent().hasClass("empty")});c.addClass("sorted")}))},a.fn.repeater.Constructor.prototype.list_getSelectedItems=function(){var b=[];return this.$canvas.find(".repeater-list .repeater-list-wrapper > table tbody tr.selected").each(function(){var c=a(this);b.push({data:c.data("item_data"),element:c})}),b},a.fn.repeater.Constructor.prototype.getValue=a.fn.repeater.Constructor.prototype.list_getSelectedItems,a.fn.repeater.Constructor.prototype.list_positionHeadings=function(){var b=this.$element.find(".repeater-list-wrapper"),c=b.offset().left,d=b.scrollLeft();d>0?b.find(".repeater-list-heading").each(function(){var b=a(this),d=b.parents("th:first").offset().left-c+"px";b.addClass("shifted").css("left",d)}):b.find(".repeater-list-heading").each(function(){a(this).removeClass("shifted").css("left","")})},a.fn.repeater.Constructor.prototype.list_setSelectedItems=function(b,c){function d(){h=a(this),f=h.data("item_data")||{},f[b[g].property]===b[g].value&&e(h,b[g].selected)}function e(a,b){b=void 0!==b?b:!0,b?(c||"multi"===j||k.list_clearSelectedItems(),a.hasClass("selected")||(a.addClass("selected"),a.find("td:first").prepend('<div class="repeater-list-check"><span class="glyphicon glyphicon-ok"></span></div>'))):(a.find(".repeater-list-check").remove(),a.removeClass("selected"))}var f,g,h,i,j=this.viewOptions.list_selectable,k=this;for(a.isArray(b)||(b=[b]),i=c===!0||"multi"===j?b.length:j&&b.length>0?1:0,g=0;i>g;g++)void 0!==b[g].index?(h=this.$canvas.find(".repeater-list table tbody tr:nth-child("+(b[g].index+1)+")"),h.length>0&&e(h,b[g].selected)):void 0!==b[g].property&&void 0!==b[g].value&&this.$canvas.find(".repeater-list table tbody tr").each(d)},a.fn.repeater.Constructor.prototype.list_sizeHeadings=function(){var b=this.$element.find(".repeater-list table");b.find("thead th").each(function(){var b=a(this),c=b.find(".repeater-list-heading");c.css({height:b.outerHeight()}),c.outerWidth(c.data("forced-width")||b.outerWidth())})},a.fn.repeater.Constructor.prototype.list_setFrozenColumns=function(){var b=this.$canvas.find(".table-frozen"),c=this.$element.find(".repeater-canvas"),d=this.$element.find(".repeater-list .repeater-list-wrapper > table"),e=this.$element.find(".repeater-list"),f=this.viewOptions.list_frozenColumns,g=this;if("multi"===this.viewOptions.list_selectable&&(f+=1,c.addClass("multi-select-enabled")),b.length<1){var h=a('<div class="frozen-column-wrapper"></div>').insertBefore(d),i=d.clone().addClass("table-frozen");i.find("th:not(:lt("+f+"))").remove(),i.find("td:not(:nth-child(n+0):nth-child(-n+"+f+"))").remove();var j=i.clone().removeClass("table-frozen");j.find("tbody").remove();var k=a('<div class="frozen-thead-wrapper"></div>').append(j);h.append(i),e.append(k),this.$canvas.addClass("frozen-enabled")}this.list_sizeFrozenColumns(),a(".frozen-thead-wrapper .repeater-list-heading").on("click",function(){var b=a(this).parent("th").index();b+=1,g.$element.find(".repeater-list-wrapper > table thead th:nth-child("+b+") .repeater-list-heading")[0].click()})},a.fn.repeater.Constructor.prototype.list_positionColumns=function(){var a=this.$element.find(".repeater-canvas"),b=a.scrollTop(),c=a.scrollLeft(),d=this.viewOptions.list_frozenColumns||"multi"===this.viewOptions.list_selectable,e=this.viewOptions.list_actions,f=this.$element.find(".repeater-canvas").outerWidth(),g=this.$element.find(".repeater-list .repeater-list-wrapper > table").outerWidth(),h=this.$element.find(".table-actions")?this.$element.find(".table-actions").outerWidth():0,i=g-(f-h)>=c;b>0?a.find(".repeater-list-heading").css("top",b):a.find(".repeater-list-heading").css("top","0"),c>0?(d&&(a.find(".frozen-thead-wrapper").css("left",c),a.find(".frozen-column-wrapper").css("left",c)),e&&i&&(a.find(".actions-thead-wrapper").css("right",-c),a.find(".actions-column-wrapper").css("right",-c))):(d&&(a.find(".frozen-thead-wrapper").css("left","0"),a.find(".frozen-column-wrapper").css("left","0")),e&&(a.find(".actions-thead-wrapper").css("right","0"),a.find(".actions-column-wrapper").css("right","0")))},a.fn.repeater.Constructor.prototype.list_createItemActions=function(){var b,c,d="",e=this,f=this.$element.find(".repeater-list .repeater-list-wrapper > table"),g=this.$canvas.find(".table-actions");for(b=0,c=this.viewOptions.list_actions.items.length;c>b;b++){
    var h=this.viewOptions.list_actions.items[b],i=h.html;d+='<li><a href="#" data-action="'+h.name+'" class="action-item"> '+i+"</a></li>"}var j='<div class="btn-group"><button type="button" class="btn btn-xs btn-default dropdown-toggle repeater-actions-button" data-toggle="dropdown" data-flip="auto" aria-expanded="false"><span class="caret"></span></button><ul class="dropdown-menu dropdown-menu-right" role="menu">'+d+"</ul></div>";if(g.length<1){var k=a('<div class="actions-column-wrapper" style="width: '+this.list_actions_width+'px"></div>').insertBefore(f),l=f.clone().addClass("table-actions");if(l.find("th:not(:last-child)").remove(),l.find("tr td:not(:last-child)").remove(),"multi"===this.viewOptions.list_selectable)l.find("thead tr").html('<th><div class="repeater-list-heading">'+j+"</div></th>"),l.find("thead .btn").attr("disabled","disabled");else{var m=this.viewOptions.list_actions.label||'<span class="actions-hidden">a</span>';l.find("thead tr").addClass("empty-heading").html("<th>"+m+'<div class="repeater-list-heading">'+m+"</div></th>")}var n=l.find("td");n.each(function(b){a(this).html(j),a(this).find("a").attr("data-row",parseInt([b])+1)}),k.append(l),this.$canvas.addClass("actions-enabled")}this.list_sizeActionsTable(),this.$element.find(".table-actions tbody .action-item").on("click",function(b){if(!e.isDisabled){var c=a(this).data("action"),d=a(this).data("row"),f={actionName:c,rows:[d]};e.list_getActionItems(f,b)}}),this.$element.find(".table-actions thead .action-item").on("click",function(b){if(!e.isDisabled){var c=a(this).data("action"),d={actionName:c,rows:[]};e.$element.find(".repeater-list-wrapper > table .selected").each(function(){var b=a(this).index();b+=1,d.rows.push(b)}),e.list_getActionItems(d,b)}})},a.fn.repeater.Constructor.prototype.list_getActionItems=function(b,c){var d,e=[],f=a.grep(this.viewOptions.list_actions.items,function(a){return a.name===b.actionName})[0];for(d=0;d<b.rows.length;d++){var g=this.$canvas.find(".repeater-list-wrapper > table tbody tr:nth-child("+b.rows[d]+")");e.push({item:g,rowData:g.data("item_data")})}if(1===e.length&&(e=e[0]),f.clickAction){var h=function(){};f.clickAction(e,h,c)}},a.fn.repeater.Constructor.prototype.list_sizeActionsTable=function(){var b=this.$element.find(".repeater-list table.table-actions"),c=b.find("thead tr th"),d=this.$element.find(".repeater-list-wrapper > table");c.outerHeight(d.find("thead tr th").outerHeight()),c.find(".repeater-list-heading").outerHeight(c.outerHeight()),b.find("tbody tr td:first-child").each(function(b,c){a(this).outerHeight(d.find("tbody tr:eq("+b+") td").outerHeight())})},a.fn.repeater.Constructor.prototype.list_sizeFrozenColumns=function(){var b=this.$element.find(".repeater-list .repeater-list-wrapper > table");this.$element.find(".repeater-list table.table-frozen tr").each(function(c){a(this).height(b.find("tr:eq("+c+")").height())});var c=b.find("td:eq(0)").outerWidth();this.$element.find(".frozen-column-wrapper, .frozen-thead-wrapper").width(c)},a.fn.repeater.Constructor.prototype.list_frozenOptionsInitialize=function(){function b(a){e.list_revertingCheckbox=!0,a.checkbox("toggle"),delete e.list_revertingCheckbox}var c=this.$element.find(".frozen-column-wrapper .checkbox-inline"),d=this.$element.find(".repeater-list table"),e=this;this.$element.find("tr.selectable").on("mouseover mouseleave",function(b){var c=a(this).index();c+=1,"mouseover"===b.type?d.find("tbody tr:nth-child("+c+")").addClass("hovered"):d.find("tbody tr:nth-child("+c+")").removeClass("hovered")}),c.checkbox(),this.$element.find(".table-frozen tbody .checkbox-inline").on("change",function(c){if(c.preventDefault(),!e.list_revertingCheckbox)if(e.isDisabled)b(a(c.currentTarget));else{var d=a(this).attr("data-row");d=parseInt(d)+1,e.$element.find(".repeater-list-wrapper > table tbody tr:nth-child("+d+")").click()}}),this.$element.find(".frozen-thead-wrapper thead .checkbox-inline").on("change",function(d){e.list_revertingCheckbox||(e.isDisabled?b(a(d.currentTarget)):a(this).checkbox("isChecked")?(e.$element.find(".repeater-list-wrapper > table tbody tr:not(.selected)").click(),e.$element.trigger("selected.fu.repeaterList",c)):(e.$element.find(".repeater-list-wrapper > table tbody tr.selected").click(),e.$element.trigger("deselected.fu.repeaterList",c)))})},a.fn.repeater.defaults=a.extend({},a.fn.repeater.defaults,{list_columnRendered:null,list_columnSizing:!0,list_columnSyncing:!0,list_highlightSortedColumn:!0,list_infiniteScroll:!1,list_noItemsHTML:"no items found",list_selectable:!1,list_sortClearing:!1,list_rowRendered:null,list_frozenColumns:0,list_actions:!1}),a.fn.repeater.viewTypes.list={cleared:function(){this.viewOptions.list_columnSyncing&&this.list_sizeHeadings()},dataOptions:function(a){return this.list_sortDirection&&(a.sortDirection=this.list_sortDirection),this.list_sortProperty&&(a.sortProperty=this.list_sortProperty),a},enabled:function(a){this.viewOptions.list_actions&&(a.status?(this.$canvas.find(".repeater-actions-button").removeAttr("disabled"),j.call(this)):this.$canvas.find(".repeater-actions-button").attr("disabled","disabled"))},initialize:function(a,b){this.list_sortDirection=null,this.list_sortProperty=null,this.list_specialBrowserClass=i(),this.list_actions_width=void 0!==this.viewOptions.list_actions.width?this.viewOptions.list_actions.width:37,this.list_noItems=!1,b()},resize:function(){h.call(this,this.$element.find(".repeater-list-wrapper > table thead tr")),this.viewOptions.list_actions&&this.list_sizeActionsTable(),(this.viewOptions.list_frozenColumns||"multi"===this.viewOptions.list_selectable)&&this.list_sizeFrozenColumns(),this.viewOptions.list_columnSyncing&&this.list_sizeHeadings()},selected:function(){var a,b=this.viewOptions.list_infiniteScroll;this.list_firstRender=!0,this.$loader.addClass("noHeader"),b&&(a="object"==typeof b?b:{},this.infiniteScrolling(!0,a))},before:function(b){var c,d=b.container.find(".repeater-list"),e=this;return d.length<1&&(d=a('<div class="repeater-list '+this.list_specialBrowserClass+'" data-preserve="shallow"><div class="repeater-list-wrapper" data-infinite="true" data-preserve="shallow"><table aria-readonly="true" class="table" data-preserve="shallow" role="grid"></table></div></div>'),d.find(".repeater-list-wrapper").on("scroll.fu.repeaterList",function(){e.viewOptions.list_columnSyncing&&e.list_positionHeadings()}),(e.viewOptions.list_frozenColumns||e.viewOptions.list_actions||"multi"===e.viewOptions.list_selectable)&&b.container.on("scroll.fu.repeaterList",function(){e.list_positionColumns()}),b.container.append(d)),b.container.removeClass("actions-enabled actions-enabled multi-select-enabled"),c=d.find("table"),g.call(this,c,b.data),f.call(this,c,b.data),!1},renderItem:function(a){return e.call(this,a.container,a.subset,a.index),!1},after:function(){var a;return!this.viewOptions.list_frozenColumns&&"multi"!==this.viewOptions.list_selectable||this.list_noItems||this.list_setFrozenColumns(),this.viewOptions.list_actions&&!this.list_noItems&&(this.list_createItemActions(),this.list_sizeActionsTable()),!this.viewOptions.list_frozenColumns&&!this.viewOptions.list_actions&&"multi"!==this.viewOptions.list_selectable||this.list_noItems||(this.list_positionColumns(),this.list_frozenOptionsInitialize()),this.viewOptions.list_columnSyncing&&(this.list_sizeHeadings(),this.list_positionHeadings()),a=this.$canvas.find(".repeater-list-wrapper > table .repeater-list-heading.sorted"),a.length>0&&this.list_highlightColumn(a.data("fu_item_index")),!1}})}(a),function(a){function b(b,c){function d(){var d,f,g;f=c.indexOf("{{"),d=c.indexOf("}}",f+2),f>-1&&d>-1?(g=a.trim(c.substring(f+2,d)),g=void 0!==b[g]?b[g]:"",c=c.substring(0,f)+g+c.substring(d+2)):e=!0}for(var e=!1;!e&&c.search("{{")>=0;)d(c);return c}a.fn.repeater&&(a.fn.repeater.Constructor.prototype.thumbnail_clearSelectedItems=function(){this.$canvas.find(".repeater-thumbnail-cont .selectable.selected").removeClass("selected")},a.fn.repeater.Constructor.prototype.thumbnail_getSelectedItems=function(){var b=[];return this.$canvas.find(".repeater-thumbnail-cont .selectable.selected").each(function(){b.push(a(this))}),b},a.fn.repeater.Constructor.prototype.thumbnail_setSelectedItems=function(b,c){function d(){return j===b[g].index?(h=a(this),!1):void j++}function e(){h=a(this),h.is(b[g].selector)&&f(h,b[g].selected)}function f(a,b){b=void 0!==b?b:!0,b?(c||"multi"===k||l.thumbnail_clearSelectedItems(),a.addClass("selected")):a.removeClass("selected")}var g,h,i,j,k=this.viewOptions.thumbnail_selectable,l=this;for(a.isArray(b)||(b=[b]),i=c===!0||"multi"===k?b.length:k&&b.length>0?1:0,g=0;i>g;g++)void 0!==b[g].index?(h=a(),j=0,this.$canvas.find(".repeater-thumbnail-cont .selectable").each(d),h.length>0&&f(h,b[g].selected)):b[g].selector&&this.$canvas.find(".repeater-thumbnail-cont .selectable").each(e)},a.fn.repeater.defaults=a.extend({},a.fn.repeater.defaults,{thumbnail_alignment:"left",thumbnail_infiniteScroll:!1,thumbnail_itemRendered:null,thumbnail_noItemsHTML:"no items found",thumbnail_selectable:!1,thumbnail_template:'<div class="thumbnail repeater-thumbnail"><img height="75" src="{{src}}" width="65"><span>{{name}}</span></div>'}),a.fn.repeater.viewTypes.thumbnail={selected:function(){var a,b=this.viewOptions.thumbnail_infiniteScroll;b&&(a="object"==typeof b?b:{},this.infiniteScrolling(!0,a))},before:function(b){var c,d,e=this.viewOptions.thumbnail_alignment,f=this.$canvas.find(".repeater-thumbnail-cont"),g=b.data,h={};return f.length<1?(f=a('<div class="clearfix repeater-thumbnail-cont" data-container="true" data-infinite="true" data-preserve="shallow"></div>'),e&&"none"!==e?(d={center:1,justify:1,left:1,right:1},e=d[e]?e:"justify",f.addClass("align-"+e),this.thumbnail_injectSpacers=!0):this.thumbnail_injectSpacers=!1,h.item=f):h.action="none",g.items&&g.items.length<1?(c=a('<div class="empty"></div>'),c.append(this.viewOptions.thumbnail_noItemsHTML),f.append(c)):f.find(".empty:first").remove(),h},renderItem:function(c){var d=this.viewOptions.thumbnail_selectable,e="selected",f=this,g=a(b(c.subset[c.index],this.viewOptions.thumbnail_template));return d&&(g.addClass("selectable"),g.on("click",function(){f.isDisabled||(g.hasClass(e)?(g.removeClass(e),f.$element.trigger("deselected.fu.repeaterThumbnail",g)):("multi"!==d&&f.$canvas.find(".repeater-thumbnail-cont .selectable.selected").each(function(){var b=a(this);b.removeClass(e),f.$element.trigger("deselected.fu.repeaterThumbnail",b)}),g.addClass(e),f.$element.trigger("selected.fu.repeaterThumbnail",g)))})),c.container.append(g),this.thumbnail_injectSpacers&&g.after('<span class="spacer">&nbsp;</span>'),this.viewOptions.thumbnail_itemRendered&&this.viewOptions.thumbnail_itemRendered({container:c.container,item:g,itemData:c.subset[c.index]},function(){}),!1}})}(a),function(a){var b=a.fn.scheduler,c=function(b,c){var d=this;this.$element=a(b),this.options=a.extend({},a.fn.scheduler.defaults,c),this.$startDate=this.$element.find(".start-datetime .start-date"),this.$startTime=this.$element.find(".start-datetime .start-time"),this.$timeZone=this.$element.find(".timezone-container .timezone"),this.$repeatIntervalPanel=this.$element.find(".repeat-every-panel"),this.$repeatIntervalSelect=this.$element.find(".repeat-options"),this.$repeatIntervalSpinbox=this.$element.find(".repeat-every"),this.$repeatIntervalTxt=this.$element.find(".repeat-every-text"),this.$end=this.$element.find(".repeat-end"),this.$endSelect=this.$end.find(".end-options"),this.$endAfter=this.$end.find(".end-after"),this.$endDate=this.$end.find(".end-on-date"),this.$recurrencePanels=this.$element.find(".repeat-panel"),this.$repeatIntervalSelect.selectlist(),this.$element.find(".selectlist").selectlist(),this.$startDate.datepicker(this.options.startDateOptions);var e="function"==typeof this.options.startDateChanged?this.options.startDateChanged:this._guessEndDate;this.$startDate.on("change changed.fu.datepicker dateClicked.fu.datepicker",a.proxy(e,this)),this.$startTime.combobox(),""===this.$startTime.find("input").val()&&this.$startTime.combobox("selectByIndex",0),"0"===this.$repeatIntervalSpinbox.find("input").val()?this.$repeatIntervalSpinbox.spinbox({value:1,min:1,limitToStep:!0}):this.$repeatIntervalSpinbox.spinbox({min:1,limitToStep:!0}),this.$endAfter.spinbox({value:1,min:1,limitToStep:!0}),this.$endDate.datepicker(this.options.endDateOptions),this.$element.find(".radio-custom").radio(),this.$repeatIntervalSelect.on("changed.fu.selectlist",a.proxy(this.repeatIntervalSelectChanged,this)),this.$endSelect.on("changed.fu.selectlist",a.proxy(this.endSelectChanged,this)),this.$element.find(".repeat-days-of-the-week .btn-group .btn").on("change.fu.scheduler",function(a,b){d.changed(a,b,!0)}),this.$element.find(".combobox").on("changed.fu.combobox",a.proxy(this.changed,this)),this.$element.find(".datepicker").on("changed.fu.datepicker",a.proxy(this.changed,this)),this.$element.find(".datepicker").on("dateClicked.fu.datepicker",a.proxy(this.changed,this)),this.$element.find(".selectlist").on("changed.fu.selectlist",a.proxy(this.changed,this)),this.$element.find(".spinbox").on("changed.fu.spinbox",a.proxy(this.changed,this)),this.$element.find(".repeat-monthly .radio-custom, .repeat-yearly .radio-custom").on("change.fu.scheduler",a.proxy(this.changed,this))},d=function(a,b){var c,d="";return d+=a.getFullYear(),d+=b,c=a.getMonth()+1,d+=10>c?"0"+c:c,d+=b,c=a.getDate(),d+=10>c?"0"+c:c},e=1e3,f=60*e,g=60*f,h=24*g,i=7*h,j=5*i,k=52*i,l={secondly:e,minutely:f,hourly:g,daily:h,weekly:i,monthly:j,yearly:k},m=function(a,b,c,d){return new Date(a.getTime()+l[c]*d)};c.prototype={constructor:c,destroy:function(){var b;return this.$element.find("input").each(function(){a(this).attr("value",a(this).val())}),this.$element.find(".datepicker .calendar").empty(),b=this.$element[0].outerHTML,this.$element.find(".combobox").combobox("destroy"),this.$element.find(".datepicker").datepicker("destroy"),this.$element.find(".selectlist").selectlist("destroy"),this.$element.find(".spinbox").spinbox("destroy"),this.$element.find(".radio-custom").radio("destroy"),this.$element.remove(),b},changed:function(b,c,d){d||b.stopPropagation(),this.$element.trigger("changed.fu.scheduler",{data:void 0!==c?c:a(b.currentTarget).data(),originalEvent:b,value:this.getValue()})},disable:function(){this.toggleState("disable")},enable:function(){this.toggleState("enable")},setUtcTime:function(a,b,c){var d=a.split("-"),e=b.split(":"),f=new Date(Date.UTC(d[0],d[1]-1,d[2],e[0],e[1],e[2]?e[2]:0));if("Z"===c)f.setUTCHours(f.getUTCHours()+0);else{var g="(.)",h=".*?",i="\\d",j=".*?",k="(\\d)",l=new RegExp(g+h+i+j+k,["i"]),m=l.exec(c);if(null!==m){var n=m[1],o=m[2],p="+"===n?1:-1;f.setUTCHours(f.getUTCHours()+p*parseInt(o,10))}}var q=f.getTimezoneOffset();return f.setMinutes(q),f},endSelectChanged:function(a,b){var c,d;b?d=b.value:(c=this.$endSelect.selectlist("selectedItem"),d=c.value),this.$endAfter.parent().addClass("hidden"),this.$endAfter.parent().attr("aria-hidden","true"),this.$endDate.parent().addClass("hidden"),this.$endDate.parent().attr("aria-hidden","true"),"after"===d?(this.$endAfter.parent().removeClass("hide hidden"),this.$endAfter.parent().attr("aria-hidden","false")):"date"===d&&(this.$endDate.parent().removeClass("hide hidden"),this.$endDate.parent().attr("aria-hidden","false"))},_guessEndDate:function(){var a=this.$repeatIntervalSelect.selectlist("selectedItem").value,b=new Date(this.$endDate.datepicker("getDate")),c=new Date(this.$startDate.datepicker("getDate")),d=this.$repeatIntervalSpinbox.find("input").val();"none"!==a&&c>=b&&(this.$repeatIntervalSpinbox.is(":visible")||(d=1),"weekdays"===a&&(d=1,a="weekly"),b=m(c,b,a,d),this.$endDate.datepicker("setDate",b))},getValue:function(){var b,c=this.$repeatIntervalSpinbox.spinbox("value"),e="",f=this.$repeatIntervalSelect.selectlist("selectedItem").value;this.$startTime.combobox("selectedItem").value?(b=this.$startTime.combobox("selectedItem").value,b=b.toLowerCase()):b=this.$startTime.combobox("selectedItem").text.toLowerCase();var g,h,i,j,k,l,m,n,o=this.$timeZone.selectlist("selectedItem");m=""+d(this.$startDate.datepicker("getDate"),"-"),m+="T",i=b.search("am")>=0,j=b.search("pm")>=0,b=a.trim(b.replace(/am/g,"").replace(/pm/g,"")).split(":"),b[0]=parseInt(b[0],10),b[1]=parseInt(b[1],10),i&&b[0]>11?b[0]=0:j&&b[0]<12&&(b[0]+=12),m+=b[0]<10?"0"+b[0]:b[0],m+=":",m+=b[1]<10?"0"+b[1]:b[1],m+="+00:00"===o.offset?"Z":o.offset,"none"===f?e="FREQ=DAILY;INTERVAL=1;COUNT=1;":"secondly"===f?(e="FREQ=SECONDLY;",e+="INTERVAL="+c+";"):"minutely"===f?(e="FREQ=MINUTELY;",e+="INTERVAL="+c+";"):"hourly"===f?(e="FREQ=HOURLY;",e+="INTERVAL="+c+";"):"daily"===f?(e+="FREQ=DAILY;",e+="INTERVAL="+c+";"):"weekdays"===f?(e+="FREQ=WEEKLY;",e+="BYDAY=MO,TU,WE,TH,FR;",e+="INTERVAL=1;"):"weekly"===f?(h=[],this.$element.find(".repeat-days-of-the-week .btn-group input:checked").each(function(){h.push(a(this).data().value)}),e+="FREQ=WEEKLY;",e+="BYDAY="+h.join(",")+";",e+="INTERVAL="+c+";"):"monthly"===f?(e+="FREQ=MONTHLY;",e+="INTERVAL="+c+";",n=this.$element.find("input[name=repeat-monthly]:checked").val(),"bymonthday"===n?(g=parseInt(this.$element.find(".repeat-monthly-date .selectlist").selectlist("selectedItem").text,10),e+="BYMONTHDAY="+g+";"):"bysetpos"===n&&(h=this.$element.find(".repeat-monthly-day .month-days").selectlist("selectedItem").value,l=this.$element.find(".repeat-monthly-day .month-day-pos").selectlist("selectedItem").value,e+="BYDAY="+h+";",e+="BYSETPOS="+l+";")):"yearly"===f&&(e+="FREQ=YEARLY;",n=this.$element.find("input[name=repeat-yearly]:checked").val(),"bymonthday"===n?(k=this.$element.find(".repeat-yearly-date .year-month").selectlist("selectedItem").value,g=this.$element.find(".repeat-yearly-date .year-month-day").selectlist("selectedItem").text,e+="BYMONTH="+k+";",e+="BYMONTHDAY="+g+";"):"bysetpos"===n&&(h=this.$element.find(".repeat-yearly-day .year-month-days").selectlist("selectedItem").value,l=this.$element.find(".repeat-yearly-day .year-month-day-pos").selectlist("selectedItem").value,k=this.$element.find(".repeat-yearly-day .year-month").selectlist("selectedItem").value,e+="BYDAY="+h+";",e+="BYSETPOS="+l+";",e+="BYMONTH="+k+";"));var p=this.$endSelect.selectlist("selectedItem").value,q="";"none"!==f&&("after"===p?q="COUNT="+this.$endAfter.spinbox("value")+";":"date"===p&&(q="UNTIL="+d(this.$endDate.datepicker("getDate"),"")+";")),e+=q,e=";"===e.substring(e.length-1)?e.substring(0,e.length-1):e;var r={startDateTime:m,timeZone:o,recurrencePattern:e};return r},repeatIntervalSelectChanged:function(a,b){var c,d,e;switch(b?(d=b.value,e=b.text):(c=this.$repeatIntervalSelect.selectlist("selectedItem"),d=c.value||"",e=c.text||""),this.$repeatIntervalTxt.text(e),d.toLowerCase()){case"hourly":case"daily":case"weekly":case"monthly":this.$repeatIntervalPanel.removeClass("hide hidden"),this.$repeatIntervalPanel.attr("aria-hidden","false");break;default:this.$repeatIntervalPanel.addClass("hidden"),this.$repeatIntervalPanel.attr("aria-hidden","true")}this.$recurrencePanels.addClass("hidden"),this.$recurrencePanels.attr("aria-hidden","true"),this.$element.find(".repeat-"+d).removeClass("hide hidden"),this.$element.find(".repeat-"+d).attr("aria-hidden","false"),"none"===d?(this.$end.addClass("hidden"),this.$end.attr("aria-hidden","true")):(this.$end.removeClass("hide hidden"),this.$end.attr("aria-hidden","false")),this._guessEndDate()},setValue:function(b){var c,d,e,f,g,h,i,j,k,l,m;if(b.startDateTime)j=b.startDateTime.split("T"),k=j[0],j[1]?(j[1]=j[1].split(":"),c=parseInt(j[1][0],10),g=j[1][1]?parseInt(j[1][1].split("+")[0].split("-")[0].split("Z")[0],10):0,h=12>c?"AM":"PM",0===c?c=12:c>12&&(c-=12),g=10>g?"0"+g:g,l=c+":"+g,j=c+":"+g+" "+h,this.$startTime.find("input").val(j),this.$startTime.combobox("selectByText",j)):l="00:00";else{l="00:00";var n=this.$startDate.datepicker("getDate");k=n.getFullYear()+"-"+n.getMonth()+"-"+n.getDate()}if(e="li",b.timeZone?("string"==typeof b.timeZone?e+='[data-name="'+b.timeZone+'"]':a.each(b.timeZone,function(a,b){e+="[data-"+a+'="'+b+'"]'}),m=b.timeZone.offset,this.$timeZone.selectlist("selectBySelector",e)):b.startDateTime?(j=b.startDateTime.split("T")[1],j=j?j.search(/\+/)>-1?"+"+a.trim(j.split("+")[1]):j.search(/\-/)>-1?"-"+a.trim(j.split("-")[1]):"+00:00":"+00:00",m="+00:00"===j?"Z":j,e+='[data-offset="'+j+'"]',this.$timeZone.selectlist("selectBySelector",e)):m="Z",b.recurrencePattern){for(i={},j=b.recurrencePattern.toUpperCase().split(";"),d=0,f=j.length;f>d;d++)""!==j[d]&&(e=j[d].split("="),i[e[0]]=e[1]);if("DAILY"===i.FREQ)e="MO,TU,WE,TH,FR"===i.BYDAY?"weekdays":"1"===i.INTERVAL&&"1"===i.COUNT?"none":"daily";else if("SECONDLY"===i.FREQ)e="secondly";else if("MINUTELY"===i.FREQ)e="minutely";else if("HOURLY"===i.FREQ)e="hourly";else if("WEEKLY"===i.FREQ){if(e="weekly",i.BYDAY)if("MO,TU,WE,TH,FR"===i.BYDAY)e="weekdays";else{var o=this.$element.find(".repeat-days-of-the-week .btn-group");for(o.find("label").removeClass("active"),j=i.BYDAY.split(","),d=0,f=j.length;f>d;d++)o.find('input[data-value="'+j[d]+'"]').prop("checked",!0).parent().addClass("active")}}else"MONTHLY"===i.FREQ?(this.$element.find(".repeat-monthly input").removeAttr("checked").removeClass("checked"),this.$element.find(".repeat-monthly label.radio-custom").removeClass("checked"),i.BYMONTHDAY?(j=this.$element.find(".repeat-monthly-date"),j.find("input").addClass("checked").prop("checked",!0),j.find("label.radio-custom").addClass("checked"),j.find(".selectlist").selectlist("selectByValue",i.BYMONTHDAY)):i.BYDAY&&(j=this.$element.find(".repeat-monthly-day"),j.find("input").addClass("checked").prop("checked",!0),j.find("label.radio-custom").addClass("checked"),i.BYSETPOS&&j.find(".month-day-pos").selectlist("selectByValue",i.BYSETPOS),j.find(".month-days").selectlist("selectByValue",i.BYDAY)),e="monthly"):"YEARLY"===i.FREQ?(this.$element.find(".repeat-yearly input").removeAttr("checked").removeClass("checked"),this.$element.find(".repeat-yearly label.radio-custom").removeClass("checked"),i.BYMONTHDAY?(j=this.$element.find(".repeat-yearly-date"),j.find("input").addClass("checked").prop("checked",!0),j.find("label.radio-custom").addClass("checked"),i.BYMONTH&&j.find(".year-month").selectlist("selectByValue",i.BYMONTH),j.find(".year-month-day").selectlist("selectByValue",i.BYMONTHDAY)):i.BYSETPOS&&(j=this.$element.find(".repeat-yearly-day"),j.find("input").addClass("checked").prop("checked",!0),j.find("label.radio-custom").addClass("checked"),j.find(".year-month-day-pos").selectlist("selectByValue",i.BYSETPOS),i.BYDAY&&j.find(".year-month-days").selectlist("selectByValue",i.BYDAY),i.BYMONTH&&j.find(".year-month").selectlist("selectByValue",i.BYMONTH)),e="yearly"):e="none";if(i.COUNT)this.$endAfter.spinbox("value",parseInt(i.COUNT,10)),this.$endSelect.selectlist("selectByValue","after");else if(i.UNTIL){j=i.UNTIL,8===j.length&&(j=j.split(""),j.splice(4,0,"-"),j.splice(7,0,"-"),j=j.join(""));var p=this.$timeZone.selectlist("selectedItem"),q="+00:00"===p.offset?"Z":p.offset,r=this.setUtcTime(j,l,q);this.$endDate.datepicker("setDate",r),this.$endSelect.selectlist("selectByValue","date")}else this.$endSelect.selectlist("selectByValue","never");this.endSelectChanged(),i.INTERVAL&&this.$repeatIntervalSpinbox.spinbox("value",parseInt(i.INTERVAL,10)),this.$repeatIntervalSelect.selectlist("selectByValue",e),this.repeatIntervalSelectChanged()}var s=this.setUtcTime(k,l,m);this.$startDate.datepicker("setDate",s)},toggleState:function(a){this.$element.find(".combobox").combobox(a),this.$element.find(".datepicker").datepicker(a),this.$element.find(".selectlist").selectlist(a),this.$element.find(".spinbox").spinbox(a),this.$element.find(".radio-custom").radio(a),a="disable"===a?"addClass":"removeClass",this.$element.find(".repeat-days-of-the-week .btn-group")[a]("disabled")},value:function(a){return a?this.setValue(a):this.getValue()}},a.fn.scheduler=function(b){var d,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.scheduler"),h="object"==typeof b&&b;g||f.data("fu.scheduler",g=new c(this,h)),"string"==typeof b&&(d=g[b].apply(g,e))});return void 0===d?f:d},a.fn.scheduler.defaults={},a.fn.scheduler.Constructor=c,a.fn.scheduler.noConflict=function(){return a.fn.scheduler=b,this},a(document).on("mousedown.fu.scheduler.data-api","[data-initialize=scheduler]",function(b){var c=a(b.target).closest(".scheduler");c.data("fu.scheduler")||c.scheduler(c.data())}),a(function(){a("[data-initialize=scheduler]").each(function(){var b=a(this);b.data("scheduler")||b.scheduler(b.data())})})}(a),function(a){var b=a.fn.picker,c=function(b,c){var d=this;this.$element=a(b),this.options=a.extend({},a.fn.picker.defaults,c),this.$accept=this.$element.find(".picker-accept"),this.$cancel=this.$element.find(".picker-cancel"),this.$trigger=this.$element.find(".picker-trigger"),this.$footer=this.$element.find(".picker-footer"),this.$header=this.$element.find(".picker-header"),this.$popup=this.$element.find(".picker-popup"),this.$body=this.$element.find(".picker-body"),this.clickStamp="_",this.isInput=this.$trigger.is("input"),this.$trigger.on("keydown.fu.picker",a.proxy(this.keyComplete,this)),this.$trigger.on("focus.fu.picker",a.proxy(function(b){("undefined"==typeof b||a(b.target).is("input[type=text]"))&&a.proxy(this.show(),this)},this)),this.$trigger.on("click.fu.picker",a.proxy(function(b){a(b.target).is("input[type=text]")?a.proxy(this.show(),this):a.proxy(this.toggle(),this)},this)),this.$accept.on("click.fu.picker",a.proxy(this.complete,this,"accepted")),this.$cancel.on("click.fu.picker",function(a){a.preventDefault(),d.complete("cancelled")})},d=function(b){var c=Math.max(document.documentElement.clientHeight,window.innerHeight||0),d=a(document).scrollTop(),e=b.$popup.offset(),f=e.top+b.$popup.outerHeight(!0);return f>c+d||e.top<d?!0:!1},e=function(a){a.$popup.css("visibility","hidden"),g(a),d(a)&&(f(a),d(a)&&g(a)),a.$popup.css("visibility","visible")},f=function(a){a.$popup.css("top",-a.$popup.outerHeight(!0)+"px")},g=function(a){a.$popup.css("top",a.$trigger.outerHeight(!0)+"px")};c.prototype={constructor:c,complete:function(a){var b={accepted:"onAccept",cancelled:"onCancel",exited:"onExit"},c=this.options[b[a]],d={contents:this.$body};c?(c(d),this.$element.trigger(a+".fu.picker",d)):(this.$element.trigger(a+".fu.picker",d),this.hide())},keyComplete:function(a){this.isInput&&13===a.keyCode?(this.complete("accepted"),this.$trigger.blur()):27===a.keyCode&&(this.complete("exited"),this.$trigger.blur())},destroy:function(){return this.$element.remove(),a(document).off("click.fu.picker.externalClick."+this.clickStamp),this.$element[0].outerHTML},disable:function(){this.$element.addClass("disabled"),this.$trigger.attr("disabled","disabled")},enable:function(){this.$element.removeClass("disabled"),this.$trigger.removeAttr("disabled")},toggle:function(){this.$element.hasClass("showing")?this.hide():this.show()},hide:function(){this.$element.hasClass("showing")&&(this.$element.removeClass("showing"),a(document).off("click.fu.picker.externalClick."+this.clickStamp),this.$element.trigger("hidden.fu.picker"))},externalClickListener:function(a,b){(b===!0||this.isExternalClick(a))&&this.complete("exited")},isExternalClick:function(b){var c,d,e=this.$element.get(0),f=this.options.externalClickExceptions||[],g=a(b.target);if(b.target===e||g.parents(".picker:first").get(0)===e)return!1;for(c=0,d=f.length;d>c;c++)if(g.is(f[c])||g.parents(f[c]).length>0)return!1;return!0},show:function(){var b;if(b=a(document).find(".picker.showing"),b.length>0){if(b.data("fu.picker")&&b.data("fu.picker").options.explicit)return;b.picker("externalClickListener",{},!0)}this.$element.addClass("showing"),e(this),this.$element.trigger("shown.fu.picker"),this.clickStamp=(new Date).getTime()+(Math.floor(100*Math.random())+1),this.options.explicit||a(document).on("click.fu.picker.externalClick."+this.clickStamp,a.proxy(this.externalClickListener,this))}},a.fn.picker=function(b){var d,e=Array.prototype.slice.call(arguments,1),f=this.each(function(){var f=a(this),g=f.data("fu.picker"),h="object"==typeof b&&b;g||f.data("fu.picker",g=new c(this,h)),"string"==typeof b&&(d=g[b].apply(g,e))});return void 0===d?f:d},a.fn.picker.defaults={onAccept:void 0,onCancel:void 0,onExit:void 0,externalClickExceptions:[],explicit:!1},a.fn.picker.Constructor=c,a.fn.picker.noConflict=function(){return a.fn.picker=b,this},a(document).on("focus.fu.picker.data-api","[data-initialize=picker]",function(b){var c=a(b.target).closest(".picker");c.data("fu.picker")||c.picker(c.data())}),a(function(){a("[data-initialize=picker]").each(function(){var b=a(this);b.data("fu.picker")||b.picker(b.data())})})}(a)});