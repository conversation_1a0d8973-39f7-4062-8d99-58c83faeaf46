<?php if ( ! defined('BASEPATH')) exit('No direct script access allowed');

class DUMMY_CLASS_NAME extends Admin_Controller {
    /*
    | -----------------------------------------------------
    | PRODUCT NAME: 	INILABS SCHOOL MANAGEMENT SYSTEM
    | -----------------------------------------------------
    | AUTHOR:			INILABS TEAM
    | -----------------------------------------------------
    | EMAIL:			<EMAIL>
    | -----------------------------------------------------
    | COPYRIGHT:		RESERVED BY INILABS IT
    | -----------------------------------------------------
    | WEBSITE:			http://inilabs.net
    | -----------------------------------------------------
    */
    function __construct() {
        parent::__construct();
        $this->load->model("DUMMY_MODEL_NAME");
        $language = $this->session->userdata('lang');
        $this->lang->load('DUMMY_CLASS_NAME_SORT', $language);
    }

    public function index() {
        $this->data['DUMMY_CLASS_NAME_SINGULARs'] = $this->DUMMY_MODEL_NAME->get_DUMMY_CLASS_NAME_SORT();
        $this->data["subview"] = "DUMMY_CLASS_NAME_SORT_VIEW/index";
        $this->load->view('_layout_main', $this->data);
    }

    protected function rules() {
        $rules = array(
            array(
                'field' => 'title',
                'label' => $this->lang->line("DUMMY_CLASS_NAME_SORT_title"),
                'rules' => 'trim|required|xss_clean|max_length[128]'
            )
        );
        return $rules;
    }

    public function add() {
        $this->data['headerassets'] = array(
            'css' => array(
                // 'assets/datepicker/datepicker.css',
                // 'assets/editor/jquery-te-1.4.0.css'
            ),
            'js' => array(
                // 'assets/editor/jquery-te-1.4.0.min.js',
                // 'assets/datepicker/datepicker.js'
            )
        );
        if($_POST) {
            $rules = $this->rules();
            $this->form_validation->set_rules($rules);
            if ($this->form_validation->run() == FALSE) {
                $this->data["subview"] = "DUMMY_CLASS_NAME_SORT_VIEW/add";
                $this->load->view('_layout_main', $this->data);
            } else {
                $array = array(
                    "title" => $this->input->post("title"),
                );
                $this->DUMMY_MODEL_NAME->insert_DUMMY_CLASS_NAME_SORT($array);
                $this->session->set_flashdata('success', $this->lang->line('menu_success'));
                redirect(base_url("DUMMY_CLASS_NAME_SORT/index"));
            }
        } else {
            $this->data["subview"] = "DUMMY_CLASS_NAME_SORT_VIEW/add";
            $this->load->view('_layout_main', $this->data);
        }
    }

    public function edit() {
        $this->data['headerassets'] = array(
            'css' => array(
                'assets/datepicker/datepicker.css',
                'assets/editor/jquery-te-1.4.0.css'
            ),
            'js' => array(
                'assets/editor/jquery-te-1.4.0.min.js',
                'assets/datepicker/datepicker.js'
            )
        );
        $id = htmlentities(escapeString($this->uri->segment(3)));
        if((int)$id) {
            $this->data['DUMMY_CLASS_NAME_SINGULAR'] = $this->DUMMY_MODEL_NAME->get_single_DUMMY_CLASS_NAME_SORT(array('DUMMY_PRIMARY_ID' => $id));
            if($this->data['DUMMY_CLASS_NAME_SINGULAR']) {
                if($_POST) {
                    $rules = $this->rules();
                    $this->form_validation->set_rules($rules);
                    if ($this->form_validation->run() == FALSE) {
                        $this->data["subview"] = "DUMMY_CLASS_NAME_SORT_VIEW/edit";
                        $this->load->view('_layout_main', $this->data);
                    } else {
                        $array = array(
                            "title" => $this->input->post("title")
                        );

                        $this->DUMMY_MODEL_NAME->update_DUMMY_CLASS_NAME_SORT($array, $id);
                        $this->session->set_flashdata('success', $this->lang->line('menu_success'));
                        redirect(base_url("DUMMY_CLASS_NAME_SORT/index"));
                    }
                } else {
                    $this->data["subview"] = "DUMMY_CLASS_NAME_SORT_VIEW/edit";
                    $this->load->view('_layout_main', $this->data);
                }
            } else {
                $this->data["subview"] = "error";
                $this->load->view('_layout_main', $this->data);
            }
        } else {
            $this->data["subview"] = "error";
            $this->load->view('_layout_main', $this->data);
        }
    }

    public function view() {
        $id = htmlentities(escapeString($this->uri->segment(3)));
        if((int)$id) {
            $this->data['DUMMY_CLASS_NAME_SINGULAR'] = $this->DUMMY_MODEL_NAME->get_single_DUMMY_CLASS_NAME_SORT(array('DUMMY_PRIMARY_ID' => $id));
            if($this->data['DUMMY_CLASS_NAME_SINGULAR']) {
                $this->data["subview"] = "DUMMY_CLASS_NAME_SORT_VIEW/view";
                $this->load->view('_layout_main', $this->data);
            } else {
                $this->data["subview"] = "error";
                $this->load->view('_layout_main', $this->data);
            }
        } else {
            $this->data["subview"] = "error";
            $this->load->view('_layout_main', $this->data);
        }
    }

    public function delete() {
        $id = htmlentities(escapeString($this->uri->segment(3)));
        if((int)$id) {
            $this->data['DUMMY_CLASS_NAME_SINGULAR'] = $this->DUMMY_MODEL_NAME->get_single_DUMMY_CLASS_NAME_SORT(array('DUMMY_PRIMARY_ID' => $id));
            if(count($this->data['DUMMY_CLASS_NAME_SINGULAR'])) {
                $this->DUMMY_MODEL_NAME->delete_DUMMY_CLASS_NAME_SORT($id);
                $this->session->set_flashdata('success', $this->lang->line('menu_success'));
                redirect(base_url("DUMMY_CLASS_NAME_SORT/index"));
            } else {
                redirect(base_url("DUMMY_CLASS_NAME_SORT/index"));
            }
        } else {
            redirect(base_url("DUMMY_CLASS_NAME_SORT/index"));
        }
    }

    public function print_preview() {
        $id = htmlentities(escapeString($this->uri->segment(3)));
        if((int)$id) {
            $this->data['DUMMY_CLASS_NAME_SINGULAR'] = $this->DUMMY_MODEL_NAME->get_single_DUMMY_CLASS_NAME_SORT(array('DUMMY_PRIMARY_ID' => $id));
            if($this->data['DUMMY_CLASS_NAME_SINGULAR']) {
                $this->data['panel_title'] = $this->lang->line('panel_title');
                $this->printView($this->data, 'DUMMY_CLASS_NAME_SORT_VIEW/print_preview');
            } else {
                $this->data["subview"] = "error";
                $this->load->view('_layout_main', $this->data);
            }
        } else {
            $this->data["subview"] = "error";
            $this->load->view('_layout_main', $this->data);
        }
    }
    public function send_mail() {
        $id = $this->input->post('id');
        if ((int)$id) {
            $this->data['DUMMY_CLASS_NAME_SINGULAR'] = $this->DUMMY_MODEL_NAME->get_single_DUMMY_CLASS_NAME_SORT(array('DUMMY_PRIMARY_ID' => $id));
            if($this->data['DUMMY_CLASS_NAME_SINGULAR']) {
                $email = $this->input->post('to');
                $subject = $this->input->post('subject');
                $message = $this->input->post('message');

                $this->viewsendtomail($this->data['DUMMY_CLASS_NAME_SINGULAR'], 'DUMMY_CLASS_NAME_SORT_VIEW/print_preview', $email, $subject, $message);
            } else {
                $this->data["subview"] = "error";
                $this->load->view('_layout_main', $this->data);
            }
        } else {
            $this->data["subview"] = "error";
            $this->load->view('_layout_main', $this->data);
        }

    }
}
