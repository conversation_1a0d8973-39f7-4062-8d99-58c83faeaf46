<div class="box">
    <div class="box-header">
        <h3 class="box-title"><i class="fa fa-calendar"></i> <?=$this->lang->line('panel_title')?></h3>


        <ol class="breadcrumb">
            <li><a href="<?=base_url("dashboard/index")?>"><i class="fa fa-laptop"></i> <?=$this->lang->line('menu_dashboard')?></a></li>
            <li class="active"><?=$this->lang->line('panel_title')?></li>
        </ol>
    </div><!-- /.box-header -->
    <!-- form start -->
    <div class="box-body">
        <div class="row">
            <div class="col-sm-12">
                <?php if(permissionChecker('DUMMY_CLASS_NAME_SORT_add')) { ?>
                    <h5 class="page-header">
                        <a href="<?php echo base_url('DUMMY_CLASS_NAME_SORT/add') ?>">
                            <i class="fa fa-plus"></i>
                            <?=$this->lang->line('add_title')?>
                        </a>
                    </h5>
                <?php } ?>
                <div id="hide-table">
                    <table id="example1" class="table table-striped table-bordered table-hover dataTable no-footer">
                        <thead>
                            <tr>
                                <th class="col-sm-2"><?=$this->lang->line('slno')?></th>
                                <th class="col-sm-2"><?=$this->lang->line('DUMMY_CLASS_NAME_SORT_title')?></th>
                                <?php if(permissionChecker('DUMMY_CLASS_NAME_SORT_edit') || permissionChecker('DUMMY_CLASS_NAME_SORT_delete') || permissionChecker('DUMMY_CLASS_NAME_SORT_view')) { ?>
                                    <th class="col-sm-2"><?=$this->lang->line('action')?></th>
                                <?php } ?>
                            </tr>
                        </thead>
                        <tbody>
                            <?php if(count($DUMMY_CLASS_NAME_SORTs)) {$i = 1; foreach($DUMMY_CLASS_NAME_SORTs as $DUMMY_CLASS_NAME_SORT) { ?>
                                <tr>
                                    <td data-title="<?=$this->lang->line('slno')?>">
                                        <?php echo $i; ?>
                                    </td>
                                    <td data-title="<?=$this->lang->line('DUMMY_CLASS_NAME_SORT_title')?>">
                                        <?=$DUMMY_CLASS_NAME_SORT->title;?>
                                    </td>
                                    <?php if(permissionChecker('DUMMY_CLASS_NAME_SORT_edit') || permissionChecker('DUMMY_CLASS_NAME_SORT_delete') || permissionChecker('DUMMY_CLASS_NAME_SORT_view')) { ?>

                                        <td data-title="<?=$this->lang->line('action')?>">
                                            <?php echo btn_view('DUMMY_CLASS_NAME_SORT/view/'.$DUMMY_CLASS_NAME_SORT->DUMMY_PRIMARY_ID, $this->lang->line('view')) ?>
                                            <?php echo btn_edit('DUMMY_CLASS_NAME_SORT/edit/'.$DUMMY_CLASS_NAME_SORT->DUMMY_PRIMARY_ID, $this->lang->line('edit')) ?>
                                            <?php echo btn_delete('DUMMY_CLASS_NAME_SORT/delete/'.$DUMMY_CLASS_NAME_SORT->DUMMY_PRIMARY_ID, $this->lang->line('delete')) ?>
                                        </td>
                                    <?php } ?>
                                </tr>
                            <?php $i++; }} ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>