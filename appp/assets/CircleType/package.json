{"name": "circletype", "version": "2.0.9", "description": "A JavaScript library that lets you curve type on the web.", "main": "dist/circletype.min.js", "files": ["dist"], "repository": "peterhry/CircleType", "scripts": {"test": "jest src --notify && node ./scripts/backstop.js", "backstop": "backstop test", "docs": "jsdoc2md --template README.hbs --files src/class.js > README.md"}, "keywords": ["circletype", "circular text", "curved text", "curved type", "typography"], "author": "<PERSON>", "license": "MIT", "bugs": "https://github.com/peterhry/CircleType/issues", "homepage": "https://github.com/peterhry/CircleType#readme", "devDependencies": {"babel-core": "6.26.0", "babel-loader": "7.1.2", "babel-preset-env": "1.6.0", "babel-preset-stage-0": "6.24.1", "backstopjs": "3.0.25", "eslint": "4.6.1", "eslint-config-airbnb-base": "12.0.0", "eslint-loader": "1.9.0", "eslint-plugin-import": "2.7.0", "jest": "21.0.2", "jsdoc-to-markdown": "3.0.0", "webpack": "3.5.6"}}