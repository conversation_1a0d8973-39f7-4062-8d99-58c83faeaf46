{"name": "circletype.js", "version": "2.0.0", "keywords": ["js", "css", "lettering", "typography", "curve", "arc"], "homepage": "http://circletype.labwire.ca/", "authors": ["<PERSON> <<EMAIL>>"], "description": "A JavaScript library that lets you curve type on the web", "repository": {"type": "git", "url": "git://github.com/peterhry/CircleType.git"}, "main": ["src/circletype.js", "dist/circletype.min.js"], "ignore": ["sass", "stylesheets", ".giti<PERSON>re", "config.rb", "favicon.ico", "index.html"], "license": "MIT", "dependencies": {}}