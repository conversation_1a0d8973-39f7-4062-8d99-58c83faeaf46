/*!
 * circletype 2.0.9
 * A JavaScript library that lets you curve type on the web.
 * Copyright © 2014-2017 <PERSON>
 * Licensed MIT
 * https://github.com/peterhry/CircleType#readme
 */
!function(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define([],e):"object"==typeof exports?exports.CircleType=e():t.CircleType=e()}(this,function(){return function(t){function e(n){if(i[n])return i[n].exports;var r=i[n]={i:n,l:!1,exports:{}};return t[n].call(r.exports,r,r.exports,e),r.l=!0,r.exports}var i={};return e.m=t,e.c=i,e.d=function(t,i,n){e.o(t,i)||Object.defineProperty(t,i,{configurable:!1,enumerable:!0,get:n})},e.n=function(t){var i=t&&t.__esModule?function(){return t.default}:function(){return t};return e.d(i,"a",i),i},e.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},e.p="",e(e.s=0)}([function(t,e,i){"use strict";var n=i(1),r=function(t){return t&&t.__esModule?t:{default:t}}(n);t.exports=r.default},function(t,e,i){"use strict";function n(t){return t&&t.__esModule?t:{default:t}}function r(t){if(Array.isArray(t)){for(var e=0,i=Array(t.length);e<t.length;e++)i[e]=t[e];return i}return Array.from(t)}function u(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}Object.defineProperty(e,"__esModule",{value:!0});var o=function(){function t(t,e){for(var i=0;i<e.length;i++){var n=e[i];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,n.key,n)}}return function(e,i,n){return i&&t(e.prototype,i),n&&t(e,n),e}}(),a=i(2),s=n(a),f=i(3),l=n(f),c=i(4),d=n(c),h=i(5),_=n(h),m=Math.PI,p=Math.max,v=function(){function t(e){u(this,t),this.element=e,this.originalHTML=this.element.innerHTML;var i=document.createElement("div");i.setAttribute("aria-label",e.innerText),i.style.position="relative",this.container=i,this._letters=(0,d.default)(e),this._letters.forEach(function(t){return i.appendChild(t)}),this.element.innerHTML="",this.element.appendChild(i);var n=window.getComputedStyle(this.element),r=n.fontSize,o=n.lineHeight;this._fontSize=parseFloat(r),this._lineHeight=parseFloat(o)||this._fontSize,this._metrics=this._letters.map(l.default);var a=this._metrics.reduce(function(t,e){return t+e.width},0);this._minRadius=a/m/2+this._lineHeight,this._dir=1,this._radius=this._minRadius,this._invalidate()}return o(t,[{key:"radius",value:function(t){return void 0!==t?(this._radius=p(this._minRadius,t),this._invalidate(),this):this._radius}},{key:"dir",value:function(t){return void 0!==t?(this._dir=t,this._invalidate(),this):this._dir}},{key:"refresh",value:function(){return this._invalidate()}},{key:"destroy",value:function(){return this.element.innerHTML=this.originalHTML,this}},{key:"_invalidate",value:function(){var t=this;return cancelAnimationFrame(this._raf),this._raf=requestAnimationFrame(function(){t._layout()}),this}},{key:"_layout",value:function(){var t=this,e=-1===this._dir?-this._radius+this._lineHeight:this._radius,i="center "+e/this._fontSize+"em",n=this._radius-this._lineHeight,u=this._metrics.reduce(function(t,e){var i=e.width,u=(0,s.default)(i/n);return{sum:t.sum+u,rotations:[].concat(r(t.rotations),[t.sum+u/2])}},{sum:0,rotations:[]}),o=u.rotations,a=u.sum;this._letters.forEach(function(e,n){var r=e.style,u=(-.5*a+o[n])*t._dir,s=-.5*t._metrics[n].width/t._fontSize,f="translateX("+s+"em) rotate("+u+"deg)";r.position="absolute",r.bottom=-1===t._dir?0:"auto",r.left="50%",r.transform=f,r.transformOrigin=i,r.webkitTransform=f,r.webkitTransformOrigin=i});var f=(0,_.default)(n,a)+this._lineHeight,l=(0,_.default)(this._radius,a);return this.container.style.height=p(f,l)/this._fontSize+"em",this}}]),t}();e.default=v},function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=180/Math.PI;e.default=function(t){return t*n}},function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=t.getBoundingClientRect();return{height:e.height,left:e.left+window.pageXOffset,top:e.top+window.pageYOffset,width:e.width}}},function(t,e,i){"use strict";function n(t){if(Array.isArray(t)){for(var e=0,i=Array(t.length);e<t.length;e++)i[e]=t[e];return i}return Array.from(t)}Object.defineProperty(e,"__esModule",{value:!0}),e.default=function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"span",i=document.createElement(e);return[].concat(n(t.innerText.trim())).map(function(t){var e=i.cloneNode();return e.insertAdjacentHTML("afterbegin"," "===t?"&nbsp;":t),e})}},function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=i(6),r=function(t){return t&&t.__esModule?t:{default:t}}(n);e.default=function(t,e){return t*(1-Math.cos((0,r.default)(e/2)))}},function(t,e,i){"use strict";Object.defineProperty(e,"__esModule",{value:!0});var n=Math.PI/180;e.default=function(t){return t*n}}])});