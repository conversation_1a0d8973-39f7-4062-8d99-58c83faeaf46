/* general styles */
html, body {
	padding:30px 100px;
	background:#E5E5E5
}
* {
	font-family:<PERSON><PERSON><PERSON>, Arial, sans-serif;
	color:#000
}
h1 {
	font-size:33px;
	color:#99CC00;
	margin:15px 0
}
.navigation {
	margin:10px 0 20px;
}
.navigation, .navigation a {
	color:#608000;
	font-size:12px;
}
.navigation a {
	margin-right:4px;
}
h2 {
	margin:15px 0 25px;
	color:#608000;
	font-size:14px;
	font-weight:bold
}
a:link, a:visited {
	text-decoration:underline
}
.testbutton {
	margin-bottom:30px;
	background:#3399FF;
	padding:6px 25px;
	border:#003F81 1px solid;
	color:#FFF;
	font-size:22px;
	border-radius:5px; -webkit-border-radius:5px; -moz-border-radius:5px;
	box-shadow:inset 0 5px 5px #67B3FF; -webkit-box-shadow:inset 0 5px 5px #67B3FF; -moz-box-shadow:inset 0 5px 5px #67B3FF
}
.testbutton:hover {
	background:#198CFF;
	cursor:pointer
}
.footer, .footer * {
	color:#802D00;
	font-size:15px
}

.jqte-test {
	display:block;
	margin:0 0 10px;
	padding:6px;
	width:95%;
	background:#FFF;
	border:#AAA 1px solid;
	font-size:13px;
}
textarea.jqte-test, div.jqte-test, span.jqte-test {
	min-height:100px;
}
button {
	display:block;
	margin:20px 0;
	padding:6px 0 4px;
	width:154px;
	background:#658700;
	border:#526E00 1px solid;
	color:#F3FFCF !important;
	text-align:center;
	font-size:13px;
	font-weight:bold;
	border-radius:3px; -webkit-border-radius:3px; -moz-border-radius:3px;
	box-shadow:0 0 2px #000, inset 0 1px 1px #99CC00; -webkit-box-shadow:0 0 2px #000, inset 0 1px 1px #99CC00; -moz-box-shadow:0 0 2px #000, inset 0 1px 1px #99CC00;
	cursor:pointer;
}
button:hover {
	background:#78A000;
	border-color:#658700;
	text-decoration:none !important;
	cursor:pointer
}

button:active {
	background:#3F5500;
	border-color:#2C3C00;
	color:#9CCD00 !important;
	box-shadow:0 0 3px #000,inset 0 -2px 2px #333;
	-webkit-box-shadow:0 0 3px #000, inset 0 -2px 2px #333;
	-moz-box-shadow:0 0 3px #000, inset 0 -2px 2px #333;
}