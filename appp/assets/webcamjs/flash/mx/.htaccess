# OVH Safe .htaccess - Allow common file types

# Prevent access to .htaccess itself
<Files .htaccess>
  Order allow,deny
  Deny from all
</Files>

# <IfModule mod_security.c>
#   SecFilterEngine Off
#   SecFilterScanPOST Off
# </IfModule>

<FilesMatch "\.(php|phps|php5|phtml|html|js|css|jpg|jpeg|png|gif|svg|webp|zip|rar|exe|sh|pl|py|cgi|txt|pdf)$">
  Order allow,deny
  Allow from all
</FilesMatch>

# php_flag engine on  # DO NOT USE ON OVH (causes 500 error)