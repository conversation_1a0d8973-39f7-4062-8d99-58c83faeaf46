/* resets styles */
body
{
    font-family: 'Raleway', sans-serif;

    color: #222;
    background: #f1f1f1;
}
h1, h2, h3, h4, h5, h6 {
    color: #23282d;
}
a, a:hover, a:focus, a:active {
    text-decoration: none;
}

.bg-white {
    background-color: #fff;
}

.menu-page label {
    display: block;
    font-size: 12px;
    font-weight: 400;
    color: #222;
    margin-bottom: 2px;
}

.menu-page input, .insert--url input {
    border: 1px solid #ddd;
    padding: 0 10px;
    border-radius: 0;
}
.menu-page input:focus, .insert--url input:focus {
    box-shadow: none;
    border-color: #2980b9;
}
.menu-page a:focus {
    color: #e67e22;
}
.link-to-original {
    border: 1px solid #ddd;
    padding: 5px;
}

.link-to-original span {
    font-style: italic;
    margin-right: 5px;
}
.menu-page .form-group a,
.menu-page .form-group span {
    display: inline-block;
}
.menu-page .form-group a {
    color: #2980b9;
    transition: all .4s ease 0s; 
}

.menu-page .form-group a:hover, .menu-page .form-group a:focus {
    color: #00a0d2;
}




/*menu settings*/
#menu-settings .panel-heading, #menu-item .panel-heading {
    padding: 0
}
#menu-settings .panel-heading a, #menu-item .panel-heading a {
    display: block;
    padding: 12px 15px;
    color: #23282d;
    background-color: #ffffff;
    transition: all .4s ease 0s;
    font-size: 12px;
    font-weight: 600;
    line-height: 18px;
}
#menu-settings .panel-heading a:hover, #menu-settings .panel-heading a:focus, 
#menu-item .panel-heading a:hover, #menu-item .panel-heading a:focus {
    text-decoration: none;
    color: #23282d;
    border-color: #999;
}

#menu-settings .panel-heading a, #menu-item .panel-heading a {
    position: relative;
}
#menu-settings .panel-heading a i, #menu-item .panel-heading a i {
    text-align: right;
    position: absolute;
    right: 20px;
    font-size: 18px
}
#menu-settings .panel-heading a[aria-expanded="true"] i, #menu-item .panel-heading a[aria-expanded="true"] i {
    transform: rotate(90deg);
    transition: all .4s ease 0s;
}

.with-border {
    border: 1px solid #ddd;
}
.menu-settings-header {
    padding: 10px;
    border-top: 0;
    margin-bottom: 10px;
}

.menu-settings-header .form-group {
    margin-bottom: 5px;
}
.form-group input {
    margin: 0;
    border-radius: 0px;
}
.form-group input:focus {
    box-shadow: none;
}
.form-group label {
    font-weight: 400;
    margin-left: 5px;
    cursor: pointer;
    margin-bottom: 3px;
}
.menu-settings-footer .select-btn {
    display: inline-block;
    margin-top: 10px;
    text-decoration: underline;
    color: #0073aa;
}
.menu-settings-footer .select-btn:hover {
    color: #00a0d2;
} 
.submit-btn, .save-menu {
    display: inline-block;
    color: #555;
    border: 1px solid #ccc;
    background: #f7f7f7;
    padding: 6px 12px;
    transition: all .4s ease 0s;
    box-shadow: 0 1px 0 #ccc;
    border-radius: 3px
}


.save-menu {
    display: inline-block;
    color: #fff;
    border: 1px solid;
    background: #0085ba;
    padding: 5px 12px;
    transition: all .4s ease 0s;
    box-shadow: 0 1px 0 #ccc;
    border-radius: 3px;
    border-color: #0073aa #006799 #006799;
    box-shadow: 0 1px 0 #006799;
    text-shadow: 0 -1px 1px #006799, 1px 0 1px #006799, 0 1px 1px #006799, -1px 0 1px #006799;
    font-size: 13px;
}

.save-menu:hover {
    background: #008ec2;
    border-color: #006799;
    color: #fff;
}


.submit-btn:hover, {
    background: #fafafa;
    border-color: #999999;
    color: #23282d;
}

#menu-item .panel {
    width: 50%;
}
#menu-management {
    border: 1px solid #ddd;
}

.menu-management-header {
    padding: 10px;
}

.menu-management-header {
    border-bottom: 1px solid #ddd;
}
.menu-management-header .form-group {
    margin: 0
}
.menu-management-header #menu-name.form-control {
    width: 70%;
    display: inline-block;
    height: 28px;
    margin-left: 13px;
}
#menu-post-body {
    padding: 10px;
    background: #fff;
}

#menu-post-body h3 {
    margin: 0 0 10px;
    font-size: 20px;
    text-transform: capitalize;
}

#menu-post-body p {
    font-size: 13px;
}

.menu-settings {
    border-top: 1px solid #eee;
    margin-top: 30px;
    padding: 10px 0
}
.menu-settings-group {
    margin: 0 0 10px;
    overflow: hidden;
    padding-left: 20%;
}
.menu-settings-list {
    margin: 0 0 10px;
    overflow: hidden;
    padding-left: 20%;
}

.menu-settings-list-name {
    width: 25%;
    float: left;
    padding: 3px 0 0;
    margin-left: -25%;
    color: #666;
    font-style: italic;
    display: block;
    font-size: 13px;
    border-bottom: 0;
}
.menu-settings-input {
    float: left;
    margin: 0;
    width: 100%;
}
.menu-settings-input label {
    font-weight: 400;
    font-size: 13px;
    margin-left: 5px;
    cursor: pointer;
}
.menu-management-footer {
    border-top: 1px solid #ddd;
    padding: 10px 0;
}
.delete-btn {
    display: inline-block;
    color: #a00;
    text-decoration: underline;
}
.delete-btn:hover {
    color: #dc3232;
}

#menu-settings .panel {
    border-radius: 0px !important;
    border: 1px solid #ddd;
}

#menu-settings .panel-heading {
    border-top-right-radius: 0px !important;
    border-top-left-radius: 0px !important;
}

#menu-settings .panel {
    margin-top: -1px !important;
}

#menu-post-body .panel {
    border-radius: 0px !important;
    border: 1px solid #ddd;
    margin-bottom: 5px;
}

#menu-post-body .sortable, #menu-post-body .sortable li {
    list-style-type: none;
}

#menu-settings label {
    color: #32373c !important;
}  

#menu-post-body .panel-body label{
    color: #666 !important;
    font-style: italic;
}

#menu-settings .nav-tabs>li.active>a, #menu-settings.nav-tabs>li.active>a:hover, #menu-settings .nav-tabs>li.active>a:focus {
    color: #32373c !important;
}

.form-control {
    height: 28px !important;
}

.move.ui-sortable-handle {
    font-style: italic;
}

.move.ui-sortable-handle span {
    color: #666;
}

.link-to-original.ui-sortable-handle span {
    color: #666;
}

.Data-remove {
    color: #a00 !important;
}

.Data-remove:hover {
    color: #dc3232 !important;
}

.move.ui-sortable-handle a {
    text-decoration: underline;
}

.link-to-original.ui-sortable-handle a {
    text-decoration: underline;
}

.menu-page .form-group input {
    color: #32373c;
}

.menu-click-button-menu span.pull-right {
    margin-right: 20px;
    font-weight: normal;
    color: #666;
}

#menu-post-body .placeholder {
    outline: 1px dashed #727c86;
    width: 50%;
}

#menu-settings .menu-has-error {
    border-color: #dc3232!important;
    box-shadow: 0 0 2px rgba(204,0,0,.8);
}

.nav-tabs-custom > .nav-tabs > li {
    border-top: 1px solid transparent;
}

.nav-tabs-custom > .nav-tabs > li.active > a {
    border-left-color: #CCC !important;
    border-right-color: #CCC !important;
}

.nav-tabs-custom > .nav-tabs > li.active {
    border-top-color: #CCC !important;
}

.tab-content .btn-menu-add-type {
    display: inline-block;
    color: #444;
    border: 1px solid #ccc;
    background: #f7f7f7;
    padding: 5px 12px;
    transition: all .4s ease 0s;
    box-shadow: 0 1px 0 #ccc;
    border-radius: 3px;
    font-size: 13px;
}

.tab-content .control-label.top-menu-color {
    color: #444;
    font-size: 13px;
}

.tab-content .form-control.top-meu-select-property {
    height: 30px !important;
    border-radius: 0;
    padding: 0px;
    color: #32373c;
    font-size: 13px;
}

.tab-content .btn-menu-add-type:hover {
    background-color: #fafafa;
    border-color: #999;
    color: #23282d;
}

.tab-content .form-horizontal .control-label.menu-label-for-mange-location {
    font-weight: 700;
    color: #555;
}

.tab-content select.form-control{
    height: 30px !important;
    border-radius: 0;
    padding: 3px;
}

.tab-content label.menu-label-italic-style {
    font-style: italic;
    color: #32373c !important;
}

.tab-content input.form-control[type=text] {
    padding: 3px;
}

#menu-settings.disable-menu {
    opacity: 0.6;
}

.menu-create-option {
    display: inline;
}

.menu-create-option .create-new-menu {
    color: #0073aa;
    text-decoration: underline;
    transition-property: border,background,color;
}

.menu-create-option .create-new-menu :hover {
    color: #00a0d2;
}

@media screen and (max-width: 480px) {
   .menu-create-option {
        margin: 5px 0 0 15px;
        width: 100%;
    }
    .publishing-menu-action.pull-right {
        float: left !important;
        margin-left: 13px;
        margin-top: 5px;
    }

    #menu-item .panel {
        width: 100%;
    }
}

.menu-management-header #create-menu-name.form-control {
    width: 70%;
    display: inline-block;
    height: 28px;
    margin-left: 13px;
}



