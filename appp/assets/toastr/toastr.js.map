{"version": 3, "sources": ["toastr.js"], "names": ["define", "$", "error", "message", "title", "optionsOverride", "notify", "type", "toastType", "iconClass", "getOptions", "iconClasses", "getContainer", "options", "create", "$container", "containerId", "length", "createContainer", "info", "subscribe", "callback", "listener", "success", "warning", "clear", "$toastElement", "clearOptions", "clearToast", "clearContainer", "remove", "removeToast", "children", "toastsToClear", "i", "force", "<PERSON><PERSON><PERSON><PERSON>", "duration", "hideDuration", "easing", "hideEasing", "complete", "attr", "addClass", "positionClass", "appendTo", "target", "getDefaults", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "toastClass", "debug", "showMethod", "showDuration", "showEasing", "onShown", "undefined", "onHidden", "extendedTimeOut", "timeOut", "titleClass", "messageClass", "closeHtml", "newestOnTop", "preventDuplicates", "progressBar", "publish", "args", "map", "personalizeToast", "setIcon", "setTitle", "setMessage", "setCloseButton", "setProgressBar", "setSequence", "handleEvents", "hover", "stickAround", "delayedHideToast", "onclick", "click", "hideToast", "closeButton", "$closeElement", "event", "stopPropagation", "cancelBubble", "displayToast", "hide", "intervalId", "setTimeout", "maxHideTime", "parseFloat", "<PERSON><PERSON><PERSON>", "Date", "getTime", "setInterval", "updateProgress", "prepend", "append", "$titleElement", "$messageElement", "$progressElement", "shouldExit", "previousToast", "override", "clearTimeout", "response", "state", "endTime", "stop", "percentage", "width", "extend", "toastId", "startTime", "console", "log", "toastr", "is", "version", "amd", "deps", "factory", "module", "exports", "require", "window"], "mappings": "CAaG,SAAUA,GACTA,GAAQ,UAAW,SAAUC,GACzB,MAAO,YA8BH,QAASC,GAAMC,EAASC,EAAOC,GAC3B,MAAOC,IACHC,KAAMC,EAAUN,MAChBO,UAAWC,IAAaC,YAAYT,MACpCC,QAASA,EACTE,gBAAiBA,EACjBD,MAAOA,IAIf,QAASQ,GAAaC,EAASC,GAG3B,MAFKD,KAAWA,EAAUH,KAC1BK,EAAad,EAAE,IAAMY,EAAQG,aACzBD,EAAWE,OACJF,GAEPD,IACAC,EAAaG,EAAgBL,IAE1BE,GAGX,QAASI,GAAKhB,EAASC,EAAOC,GAC1B,MAAOC,IACHC,KAAMC,EAAUW,KAChBV,UAAWC,IAAaC,YAAYQ,KACpChB,QAASA,EACTE,gBAAiBA,EACjBD,MAAOA,IAIf,QAASgB,GAAUC,GACfC,EAAWD,EAGf,QAASE,GAAQpB,EAASC,EAAOC,GAC7B,MAAOC,IACHC,KAAMC,EAAUe,QAChBd,UAAWC,IAAaC,YAAYY,QACpCpB,QAASA,EACTE,gBAAiBA,EACjBD,MAAOA,IAIf,QAASoB,GAAQrB,EAASC,EAAOC,GAC7B,MAAOC,IACHC,KAAMC,EAAUgB,QAChBf,UAAWC,IAAaC,YAAYa,QACpCrB,QAASA,EACTE,gBAAiBA,EACjBD,MAAOA,IAIf,QAASqB,GAAMC,EAAeC,GAC1B,GAAId,GAAUH,GACTK,IAAcH,EAAaC,GAC3Be,EAAWF,EAAeb,EAASc,IACpCE,EAAehB,GAIvB,QAASiB,GAAOJ,GACZ,GAAIb,GAAUH,GAEd,OADKK,IAAcH,EAAaC,GAC5Ba,GAAuD,IAAtCzB,EAAE,SAAUyB,GAAeT,WAC5Cc,GAAYL,QAGZX,EAAWiB,WAAWf,QACtBF,EAAWe,UAMnB,QAASD,GAAgBhB,GAErB,IAAK,GADDoB,GAAgBlB,EAAWiB,WACtBE,EAAID,EAAchB,OAAS,EAAGiB,GAAK,EAAGA,IAC3CN,EAAW3B,EAAEgC,EAAcC,IAAKrB,GAIxC,QAASe,GAAYF,EAAeb,EAASc,GACzC,GAAIQ,GAAQR,GAAgBA,EAAaQ,MAAQR,EAAaQ,OAAQ,CACtE,OAAIT,KAAkBS,GAA+C,IAAtClC,EAAE,SAAUyB,GAAeT,SACtDS,EAAcb,EAAQuB,aAClBC,SAAUxB,EAAQyB,aAClBC,OAAQ1B,EAAQ2B,WAChBC,SAAU,WAAcV,EAAYL,OAEjC,IAEJ,EAGX,QAASR,GAAgBL,GAQrB,MAPAE,GAAad,EAAE,UACVyC,KAAK,KAAM7B,EAAQG,aACnB2B,SAAS9B,EAAQ+B,eACjBF,KAAK,YAAa,UAClBA,KAAK,OAAQ,SAElB3B,EAAW8B,SAAS5C,EAAEY,EAAQiC,SACvB/B,EAGX,QAASgC,KACL,OACIC,cAAc,EACdC,WAAY,QACZjC,YAAa,kBACbkC,OAAO,EAEPC,WAAY,SACZC,aAAc,IACdC,WAAY,QACZC,QAASC,OACTnB,WAAY,UACZE,aAAc,IACdE,WAAY,QACZgB,SAAUD,OAEVE,gBAAiB,IACjB9C,aACIT,MAAO,cACPiB,KAAM,aACNI,QAAS,gBACTC,QAAS,iBAEbf,UAAW,aACXmC,cAAe,kBACfc,QAAS,IACTC,WAAY,cACZC,aAAc,gBACdd,OAAQ,OACRe,UAAW,yCACXC,aAAa,EACbC,mBAAmB,EACnBC,aAAa,GAIrB,QAASC,GAAQC,GACR5C,GACLA,EAAS4C,GAGb,QAAS5D,GAAO6D,GAgDZ,QAASC,KACLC,IACAC,IACAC,IACAC,IACAC,IACAC,IAGJ,QAASC,KACLjD,EAAckD,MAAMC,EAAaC,IAC5BjE,EAAQkE,SAAWlE,EAAQmC,cAC5BtB,EAAcsD,MAAMC,GAGpBpE,EAAQqE,aAAeC,GACvBA,EAAcH,MAAM,SAAUI,GACtBA,EAAMC,gBACND,EAAMC,kBACwB9B,SAAvB6B,EAAME,cAA8BF,EAAME,gBAAiB,IAClEF,EAAME,cAAe,GAEzBL,GAAU,KAIdpE,EAAQkE,SACRrD,EAAcsD,MAAM,WAChBnE,EAAQkE,UACRE,MAKZ,QAASM,KACL7D,EAAc8D,OAEd9D,EAAcb,EAAQsC,aACjBd,SAAUxB,EAAQuC,aAAcb,OAAQ1B,EAAQwC,WAAYZ,SAAU5B,EAAQyC,UAG/EzC,EAAQ6C,QAAU,IAClB+B,EAAaC,WAAWT,EAAWpE,EAAQ6C,SAC3CM,EAAY2B,YAAcC,WAAW/E,EAAQ6C,SAC7CM,EAAY6B,SAAU,GAAIC,OAAOC,UAAY/B,EAAY2B,YACrD9E,EAAQmD,cACRA,EAAYyB,WAAaO,YAAYC,EAAgB,MAKjE,QAAS5B,KACDF,EAAI1D,WACJiB,EAAciB,SAAS9B,EAAQoC,YAAYN,SAASlC,GAI5D,QAASiE,KACD7D,EAAQiD,YACR/C,EAAWmF,QAAQxE,GAEnBX,EAAWoF,OAAOzE,GAI1B,QAAS4C,KACDH,EAAI/D,QACJgG,EAAcD,OAAOhC,EAAI/D,OAAOuC,SAAS9B,EAAQ8C,YACjDjC,EAAcyE,OAAOC,IAI7B,QAAS7B,KACDJ,EAAIhE,UACJkG,EAAgBF,OAAOhC,EAAIhE,SAASwC,SAAS9B,EAAQ+C,cACrDlC,EAAcyE,OAAOE,IAI7B,QAAS7B,KACD3D,EAAQqE,cACRC,EAAcxC,SAAS,sBAAsBD,KAAK,OAAQ,UAC1DhB,EAAcwE,QAAQf,IAI9B,QAASV,KACD5D,EAAQmD,cACRsC,EAAiB3D,SAAS,kBAC1BjB,EAAcwE,QAAQI,IAI9B,QAASC,GAAW1F,EAASsD,GACzB,GAAItD,EAAQkD,kBAAmB,CAC3B,GAAII,EAAIhE,UAAYqG,EAChB,OAAO,CAEPA,GAAgBrC,EAAIhE,QAG5B,OAAO,EAGX,QAAS8E,GAAUwB,GACf,OAAIxG,EAAE,SAAUyB,GAAeT,QAAWwF,GAG1CC,aAAa1C,EAAYyB,YAClB/D,EAAcb,EAAQuB,aACzBC,SAAUxB,EAAQyB,aAClBC,OAAQ1B,EAAQ2B,WAChBC,SAAU,WACNV,EAAYL,GACRb,EAAQ2C,UAA+B,WAAnBmD,EAASC,OAC7B/F,EAAQ2C,WAEZmD,EAASC,MAAQ,SACjBD,EAASE,QAAU,GAAIf,MACvB7B,EAAQ0C,OAdhB,OAmBJ,QAAS7B,MACDjE,EAAQ6C,QAAU,GAAK7C,EAAQ4C,gBAAkB,KACjDgC,EAAaC,WAAWT,EAAWpE,EAAQ4C,iBAC3CO,EAAY2B,YAAcC,WAAW/E,EAAQ4C,iBAC7CO,EAAY6B,SAAU,GAAIC,OAAOC,UAAY/B,EAAY2B,aAIjE,QAASd,KACL6B,aAAajB,GACbzB,EAAY6B,QAAU,EACtBnE,EAAcoF,MAAK,GAAM,GAAMjG,EAAQsC,aAClCd,SAAUxB,EAAQuC,aAAcb,OAAQ1B,EAAQwC,aAIzD,QAAS4C,KACL,GAAIc,IAAe/C,EAAY6B,SAAW,GAAIC,OAAOC,WAAc/B,EAAY2B,YAAe,GAC9FW,GAAiBU,MAAMD,EAAa,KA7LxC,GAAIlG,GAAUH,IACVD,EAAY0D,EAAI1D,WAAaI,EAAQJ,SAOzC,IALqC,mBAAzB0D,GAAmB,kBAC3BtD,EAAUZ,EAAEgH,OAAOpG,EAASsD,EAAI9D,iBAChCI,EAAY0D,EAAI9D,gBAAgBI,WAAaA,IAG7C8F,EAAW1F,EAASsD,GAAxB,CAEA+C,IAEAnG,EAAaH,EAAaC,GAAS,EAEnC,IAAI4E,GAAa,KACb/D,EAAgBzB,EAAE,UAClBmG,EAAgBnG,EAAE,UAClBoG,EAAkBpG,EAAE,UACpBqG,EAAmBrG,EAAE,UACrBkF,EAAgBlF,EAAEY,EAAQgD,WAC1BG,GACAyB,WAAY,KACZI,QAAS,KACTF,YAAa,MAEbgB,GACAO,QAASA,EACTN,MAAO,UACPO,UAAW,GAAIrB,MACfjF,QAASA,EACTsD,IAAKA,EAeT,OAZAC,KAEAmB,IAEAZ,IAEAV,EAAQ0C,GAEJ9F,EAAQqC,OAASkE,SACjBA,QAAQC,IAAIV,GAGTjF,GAoJX,QAAShB,KACL,MAAOT,GAAEgH,UAAWlE,IAAeuE,EAAOzG,SAG9C,QAASkB,GAAYL,GACZX,IAAcA,EAAaH,KAC5Bc,EAAc6F,GAAG,cAGrB7F,EAAcI,SACdJ,EAAgB,KACqB,IAAjCX,EAAWiB,WAAWf,SACtBF,EAAWe,SACX0E,EAAgBjD,SAlYxB,GAAIxC,GACAO,EAsBAkF,EArBAU,EAAU,EACV1G,GACAN,MAAO,QACPiB,KAAM,OACNI,QAAS,UACTC,QAAS,WAGT8F,GACA7F,MAAOA,EACPK,OAAQA,EACR5B,MAAOA,EACPU,aAAcA,EACdO,KAAMA,EACNN,WACAO,UAAWA,EACXG,QAASA,EACTiG,QAAS,QACThG,QAASA,EAKb,OAAO8F,SA+WC,kBAAXtH,SAAyBA,OAAOyH,IAAMzH,OAAS,SAAU0H,EAAMC,GAC9C,mBAAXC,SAA0BA,OAAOC,QACxCD,OAAOC,QAAUF,EAAQG,QAAQ,WAEjCC,OAAe,OAAIJ,EAAQI,OAAe", "file": "toastr.js", "sourcesContent": ["/*\n * Toastr\n * Copyright 2012-2015\n * Authors: <AUTHORS>\n * All Rights Reserved.\n * Use, reproduction, distribution, and modification of this code is subject to the terms and\n * conditions of the MIT license, available at http://www.opensource.org/licenses/mit-license.php\n *\n * ARIA Support: <PERSON><PERSON>\n *\n * Project: https://github.com/CodeSeven/toastr\n */\n/* global define */\n; (function (define) {\n    define(['jquery'], function ($) {\n        return (function () {\n            var $container;\n            var listener;\n            var toastId = 0;\n            var toastType = {\n                error: 'error',\n                info: 'info',\n                success: 'success',\n                warning: 'warning'\n            };\n\n            var toastr = {\n                clear: clear,\n                remove: remove,\n                error: error,\n                getContainer: getContainer,\n                info: info,\n                options: {},\n                subscribe: subscribe,\n                success: success,\n                version: '2.1.1',\n                warning: warning\n            };\n\n            var previousToast;\n\n            return toastr;\n\n            ////////////////\n\n            function error(message, title, optionsOverride) {\n                return notify({\n                    type: toastType.error,\n                    iconClass: getOptions().iconClasses.error,\n                    message: message,\n                    optionsOverride: optionsOverride,\n                    title: title\n                });\n            }\n\n            function getContainer(options, create) {\n                if (!options) { options = getOptions(); }\n                $container = $('#' + options.containerId);\n                if ($container.length) {\n                    return $container;\n                }\n                if (create) {\n                    $container = createContainer(options);\n                }\n                return $container;\n            }\n\n            function info(message, title, optionsOverride) {\n                return notify({\n                    type: toastType.info,\n                    iconClass: getOptions().iconClasses.info,\n                    message: message,\n                    optionsOverride: optionsOverride,\n                    title: title\n                });\n            }\n\n            function subscribe(callback) {\n                listener = callback;\n            }\n\n            function success(message, title, optionsOverride) {\n                return notify({\n                    type: toastType.success,\n                    iconClass: getOptions().iconClasses.success,\n                    message: message,\n                    optionsOverride: optionsOverride,\n                    title: title\n                });\n            }\n\n            function warning(message, title, optionsOverride) {\n                return notify({\n                    type: toastType.warning,\n                    iconClass: getOptions().iconClasses.warning,\n                    message: message,\n                    optionsOverride: optionsOverride,\n                    title: title\n                });\n            }\n\n            function clear($toastElement, clearOptions) {\n                var options = getOptions();\n                if (!$container) { getContainer(options); }\n                if (!clearToast($toastElement, options, clearOptions)) {\n                    clearContainer(options);\n                }\n            }\n\n            function remove($toastElement) {\n                var options = getOptions();\n                if (!$container) { getContainer(options); }\n                if ($toastElement && $(':focus', $toastElement).length === 0) {\n                    removeToast($toastElement);\n                    return;\n                }\n                if ($container.children().length) {\n                    $container.remove();\n                }\n            }\n\n            // internal functions\n\n            function clearContainer (options) {\n                var toastsToClear = $container.children();\n                for (var i = toastsToClear.length - 1; i >= 0; i--) {\n                    clearToast($(toastsToClear[i]), options);\n                }\n            }\n\n            function clearToast ($toastElement, options, clearOptions) {\n                var force = clearOptions && clearOptions.force ? clearOptions.force : false;\n                if ($toastElement && (force || $(':focus', $toastElement).length === 0)) {\n                    $toastElement[options.hideMethod]({\n                        duration: options.hideDuration,\n                        easing: options.hideEasing,\n                        complete: function () { removeToast($toastElement); }\n                    });\n                    return true;\n                }\n                return false;\n            }\n\n            function createContainer(options) {\n                $container = $('<div/>')\n                    .attr('id', options.containerId)\n                    .addClass(options.positionClass)\n                    .attr('aria-live', 'polite')\n                    .attr('role', 'alert');\n\n                $container.appendTo($(options.target));\n                return $container;\n            }\n\n            function getDefaults() {\n                return {\n                    tapToDismiss: true,\n                    toastClass: 'toast',\n                    containerId: 'toast-container',\n                    debug: false,\n\n                    showMethod: 'fadeIn', //fadeIn, slideDown, and show are built into jQuery\n                    showDuration: 300,\n                    showEasing: 'swing', //swing and linear are built into jQuery\n                    onShown: undefined,\n                    hideMethod: 'fadeOut',\n                    hideDuration: 1000,\n                    hideEasing: 'swing',\n                    onHidden: undefined,\n\n                    extendedTimeOut: 1000,\n                    iconClasses: {\n                        error: 'toast-error',\n                        info: 'toast-info',\n                        success: 'toast-success',\n                        warning: 'toast-warning'\n                    },\n                    iconClass: 'toast-info',\n                    positionClass: 'toast-top-right',\n                    timeOut: 5000, // Set timeOut and extendedTimeOut to 0 to make it sticky\n                    titleClass: 'toast-title',\n                    messageClass: 'toast-message',\n                    target: 'body',\n                    closeHtml: '<button type=\"button\">&times;</button>',\n                    newestOnTop: true,\n                    preventDuplicates: false,\n                    progressBar: false\n                };\n            }\n\n            function publish(args) {\n                if (!listener) { return; }\n                listener(args);\n            }\n\n            function notify(map) {\n                var options = getOptions();\n                var iconClass = map.iconClass || options.iconClass;\n\n                if (typeof (map.optionsOverride) !== 'undefined') {\n                    options = $.extend(options, map.optionsOverride);\n                    iconClass = map.optionsOverride.iconClass || iconClass;\n                }\n\n                if (shouldExit(options, map)) { return; }\n\n                toastId++;\n\n                $container = getContainer(options, true);\n\n                var intervalId = null;\n                var $toastElement = $('<div/>');\n                var $titleElement = $('<div/>');\n                var $messageElement = $('<div/>');\n                var $progressElement = $('<div/>');\n                var $closeElement = $(options.closeHtml);\n                var progressBar = {\n                    intervalId: null,\n                    hideEta: null,\n                    maxHideTime: null\n                };\n                var response = {\n                    toastId: toastId,\n                    state: 'visible',\n                    startTime: new Date(),\n                    options: options,\n                    map: map\n                };\n\n                personalizeToast();\n\n                displayToast();\n\n                handleEvents();\n\n                publish(response);\n\n                if (options.debug && console) {\n                    console.log(response);\n                }\n\n                return $toastElement;\n\n                function personalizeToast() {\n                    setIcon();\n                    setTitle();\n                    setMessage();\n                    setCloseButton();\n                    setProgressBar();\n                    setSequence();\n                }\n\n                function handleEvents() {\n                    $toastElement.hover(stickAround, delayedHideToast);\n                    if (!options.onclick && options.tapToDismiss) {\n                        $toastElement.click(hideToast);\n                    }\n\n                    if (options.closeButton && $closeElement) {\n                        $closeElement.click(function (event) {\n                            if (event.stopPropagation) {\n                                event.stopPropagation();\n                            } else if (event.cancelBubble !== undefined && event.cancelBubble !== true) {\n                                event.cancelBubble = true;\n                            }\n                            hideToast(true);\n                        });\n                    }\n\n                    if (options.onclick) {\n                        $toastElement.click(function () {\n                            options.onclick();\n                            hideToast();\n                        });\n                    }\n                }\n\n                function displayToast() {\n                    $toastElement.hide();\n\n                    $toastElement[options.showMethod](\n                        {duration: options.showDuration, easing: options.showEasing, complete: options.onShown}\n                    );\n\n                    if (options.timeOut > 0) {\n                        intervalId = setTimeout(hideToast, options.timeOut);\n                        progressBar.maxHideTime = parseFloat(options.timeOut);\n                        progressBar.hideEta = new Date().getTime() + progressBar.maxHideTime;\n                        if (options.progressBar) {\n                            progressBar.intervalId = setInterval(updateProgress, 10);\n                        }\n                    }\n                }\n\n                function setIcon() {\n                    if (map.iconClass) {\n                        $toastElement.addClass(options.toastClass).addClass(iconClass);\n                    }\n                }\n\n                function setSequence() {\n                    if (options.newestOnTop) {\n                        $container.prepend($toastElement);\n                    } else {\n                        $container.append($toastElement);\n                    }\n                }\n\n                function setTitle() {\n                    if (map.title) {\n                        $titleElement.append(map.title).addClass(options.titleClass);\n                        $toastElement.append($titleElement);\n                    }\n                }\n\n                function setMessage() {\n                    if (map.message) {\n                        $messageElement.append(map.message).addClass(options.messageClass);\n                        $toastElement.append($messageElement);\n                    }\n                }\n\n                function setCloseButton() {\n                    if (options.closeButton) {\n                        $closeElement.addClass('toast-close-button').attr('role', 'button');\n                        $toastElement.prepend($closeElement);\n                    }\n                }\n\n                function setProgressBar() {\n                    if (options.progressBar) {\n                        $progressElement.addClass('toast-progress');\n                        $toastElement.prepend($progressElement);\n                    }\n                }\n\n                function shouldExit(options, map) {\n                    if (options.preventDuplicates) {\n                        if (map.message === previousToast) {\n                            return true;\n                        } else {\n                            previousToast = map.message;\n                        }\n                    }\n                    return false;\n                }\n\n                function hideToast(override) {\n                    if ($(':focus', $toastElement).length && !override) {\n                        return;\n                    }\n                    clearTimeout(progressBar.intervalId);\n                    return $toastElement[options.hideMethod]({\n                        duration: options.hideDuration,\n                        easing: options.hideEasing,\n                        complete: function () {\n                            removeToast($toastElement);\n                            if (options.onHidden && response.state !== 'hidden') {\n                                options.onHidden();\n                            }\n                            response.state = 'hidden';\n                            response.endTime = new Date();\n                            publish(response);\n                        }\n                    });\n                }\n\n                function delayedHideToast() {\n                    if (options.timeOut > 0 || options.extendedTimeOut > 0) {\n                        intervalId = setTimeout(hideToast, options.extendedTimeOut);\n                        progressBar.maxHideTime = parseFloat(options.extendedTimeOut);\n                        progressBar.hideEta = new Date().getTime() + progressBar.maxHideTime;\n                    }\n                }\n\n                function stickAround() {\n                    clearTimeout(intervalId);\n                    progressBar.hideEta = 0;\n                    $toastElement.stop(true, true)[options.showMethod](\n                        {duration: options.showDuration, easing: options.showEasing}\n                    );\n                }\n\n                function updateProgress() {\n                    var percentage = ((progressBar.hideEta - (new Date().getTime())) / progressBar.maxHideTime) * 100;\n                    $progressElement.width(percentage + '%');\n                }\n            }\n\n            function getOptions() {\n                return $.extend({}, getDefaults(), toastr.options);\n            }\n\n            function removeToast($toastElement) {\n                if (!$container) { $container = getContainer(); }\n                if ($toastElement.is(':visible')) {\n                    return;\n                }\n                $toastElement.remove();\n                $toastElement = null;\n                if ($container.children().length === 0) {\n                    $container.remove();\n                    previousToast = undefined;\n                }\n            }\n\n        })();\n    });\n}(typeof define === 'function' && define.amd ? define : function (deps, factory) {\n    if (typeof module !== 'undefined' && module.exports) { //Node\n        module.exports = factory(require('jquery'));\n    } else {\n        window['toastr'] = factory(window['jQuery']);\n    }\n}));\n"], "sourceRoot": "/source/"}