

@font-face {
  font-family: 'OpenSans Regular';
  src: url('../fonts/OpenSans-Regular.ttf');
  src: url('../fonts/opensans-regular-webfont.eot');
  src: url('../fonts/opensans-regular-webfont.eot?#iefix') format('embedded-opentype'),
       url('../fonts/opensans-regular-webfont.woff2') format('woff2'),
       url('../fonts/opensans-regular-webfont.woff') format('woff'),
       url('../fonts/opensans-regular-webfont.svg#open_sansregular') format('svg');
  font-weight: normal;
  font-style: normal;
}

body {
	font-family: 'helvetica';
	font-size:12px;
	color:#707478;
}

img { 
	width:50px;
	height:50px;
	text-align:center;
}

p {
	margin: 2px;
}

.title{
	font-size: 15px
}

.title-desc {
	font-size: 12px
}

.reportPage-header {
	text-align: center;
}

table {
	width:100%; 
	border-spacing:0px;
	font-size: 10px;
	padding: 0px;
	border: 1px solid #ddd;
}

th {
	font-weight: 700;
}

td, th {
	border: 0.1px solid #ddd;
	padding:6px;
}

.pull-left { 
	float: left; 
	text-align: left;
	width: 35%;
	margin: 0px;
	padding: 10px
}

.pull-right {
	float: right; 
	text-align: right;
	width: 35%;
	margin: 0px;
	padding: 10px;
}

.text-center {
	text-align: center;
}

