body {
	font-family: 'helvetica';
	font-size:12px;
	color:#707478;
}

.logo{ 
	width:50px;
	height:50px;
	text-align:center;
}

p {
	margin: 2px;
}

.title{
	font-size: 15px
}

.title-desc {
	font-size: 12px
}

.reportPage-header {
	text-align: center;
}

table {
	width:100%; 
	border-spacing:0px;
	font-size: 10px;
	padding: 0px;
	border-left: 1px solid #ddd	;
	border-right: 1px solid #ddd	;
	border-top: 1px solid #ddd	;
}

th{
	font-weight: 400;
	border-bottom: 1px solid #ddd;
	font-size: 16px;
	padding: 6px 0px
}

td{
	border-bottom: 01px solid #ddd;
	padding:6px 12px;
}

.pull-left { 
	float: left; 
	text-align: left;
	width: 48%;
	margin: 0px;
	padding: 10px 0px;
}

.pull-right {
	float: right; 
	text-align: left;
	width: 48%;
	margin: 0px;
	padding: 10px 0px;
}

.text-center {
	text-align: center;
}

.footer {
	padding-top: 10px;
	text-align: center;
}

.flogo {
	width: 30px;
	height: 30px;
}

.copyright {
	font-size: 10px
}

.notfound{
	padding: 20px 0px;
	text-align:center;
	border:1px solid #ddd;
}

.examdetails {
	border-left:2px solid black;
	border-radius: 5px;
}

.examdetails .box-body{
	padding-top: 0px;
	margin-top: 0px;
}

.row {
	width: 100%;
}
/*.col-sm-6 {
	float: left;
	width: 48%;
	display: inline-block;
	margin: 0 2%;
}*/

.examdetails-head {
	float: left;
	width: 48%;
	display: inline-block;
}

.profile-head {
	float: left;
	width: 48%;
	display: inline-block;
	margin-left: 2%;
}

.text-blue {
	color: #0073b7 !important;
}

.box-title{
	background-color:#eaeaec;
}

.box h3 {
	margin: 0px;
	padding:10px;
	color:#001f3f !important;
	font-size: 14;
	font-weight:400;
}

.box-solid {
	border:1px solid #ddd;
}

.box-body {
	padding: 15px 10px;
}



.profiledetails {
	border-left:2px solid black;
	border-radius: 5px;
}

.profiledetails .box-body{
	padding-top: 0px;
	margin-top: 0px;
}


.profile {
	padding: 0px;
	margin: 0px;
	background: #C24984;
	border: 0px solid transparent;
	border-radius: 4px;
	text-align: center;
	border-bottom: 2px solid #ddd;
}

.border_image{
	margin-top: 10px;
}

.profile-image {
	width: 50px;
	height:50px;
	border:2px solid #ccc;
}

.profile h1 {
	font-size: 16px;
	color: #fff;
	font-family: verdana !important ;
	font-weight: normal;
}

.box h2 {
	font-size: 14px;
}

.hr {
	color:#ddd;
}