.librarycardreport {
    font-family: arial;
    max-width:794px;
    max-height: 1123px;
    margin-left: auto;
    margin-right: auto;
    margin: 0px;
    padding: 0px;
}

img {
    width: 50px;
    height: 50px;
    border: 1px solid #ddd;
    margin-bottom: 10px;
}


/*IDcard Front Part Css Code*/
.librarycardreport-frontend {
    width: 205px;
    border: 1px solid #000;
    padding: 10px;
    text-align: center;
    float: left;
    display: inline-block;
    margin-bottom: 5px;
    margin-left: 5px;
    background-size: 100% 100% !important;
}

.librarycardreport-info {
    width: 100%;
    text-align: left;
    padding: 0px 5px;
}

.librarycardreport-item{
    margin-top: 2px;
    font-size: 12px;
    display: inline;
}

.left {
    width: 43%;
    float: left;
}

.right {
    width: 55%;
    float: right;
}

.librarycardreport-frontend h3 {
    color: #03143B;
    font-weight: light;
}


/* IDcard Front Part Css Code */
.librarycardreport-backend {
    width: 203px;
    border: 1px solid #000;
    padding: 12px;
    text-align: center;
    float: right;
    display: block;
    margin-bottom: 5px;
    margin-right: 5px;
    background-size: 100% 100% !important;
}

.librarycardreport-backend h3 {
    background: #000;
    color: #fff;
    padding:5px 0px;
    margin: 4px 1px;
    font-size: 10px;
    margin-top: 10px;
}

.librarycardreport-backend h4 {
    color: #000;
    padding:5px 0px;
    margin: 5px 0px;
    font-size: 10px;
}

.librarycardreport-backend p {
    color: #000;
    font-weight: normal;
    margin: 0px;
}

.librarycardreport-schooladdress{
    color: #000;
    font-size: 12px;
    font-weight: 700;
}

.librarycardreport-bottom{
    width: 100%;
    margin-top: 10px;
}

.librarycardreport-qrcode {
    width: 50%;
    float: left;
}

.librarycardreport-qrcode img{
    padding-top: 30px !important;
}

.librarycardreport-session{
    float: right;
    width: 50%;
    font-size: 10px;
    padding-top: 40px;
    font-weight: bold;
    display: block;
    text-align: right;
}

