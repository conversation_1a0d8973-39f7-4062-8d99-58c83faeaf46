body {
	font-family: 'helvetica';
	font-size:12px;
	color:#707478;
}

.logo{
	width:50px;
	height:50px;
	text-align:center;
}

p {
	margin: 2px;
}

.title{
	font-size: 15px
}

.title-desc {
	font-size: 12px
}

.reportPage-header {
	text-align: center;
}

table {
	width:100%;
	border-spacing:0px;
	font-size: 10px;
	padding: 0px;
	border: 0.1px solid #ddd;
}

th {
	font-weight: bold;
}

td, th {
	border: 0.1px solid #ddd;
	padding:6px;
	text-align: left;
}

.pull-left {
	float: left;
	text-align: left;
	width: 35%;
	margin: 0px;
	padding: 10px 0px;
}

.pull-right {
	float: right;
	text-align: right;
	width: 35%;
	margin: 0px;
	padding: 10px 0px;
}

.text-center {
	text-align: center;
}

.grand-total {
	font-weight: bold;
	text-align: right;
}

.rtext-bold {
	font-weight: bold;
}

.footer {
	padding-top: 10px;
	text-align: center;
}

.flogo {
	width: 30px;
	height: 30px;
}

.copyright {
	font-size: 10px
}

.notfound{
	padding: 20px 0px;
	text-align:center;
	border:1px solid #ddd;
}
