{"name": "iCheck", "version": "1.0.2", "description": "Highly customizable checkboxes and radio buttons (jQuery & Zepto)", "keywords": ["icheck", "checkbox", "radio", "input", "field", "form", "custom", "replacement", "accessibility", "skins", "ui", "checked", "disabled", "indeterminate"], "main": ["./icheck.min.js"], "dependencies": {"jquery": ">=1.7"}, "ignore": [".giti<PERSON>re", "CHANGELOG.md", "README.md", "demo/"], "license": "MIT", "authors": [{"name": "<PERSON><PERSON>", "email": "<EMAIL>", "homepage": "http://fronteed.com/"}], "homepage": "http://fronteed.com/iCheck/"}