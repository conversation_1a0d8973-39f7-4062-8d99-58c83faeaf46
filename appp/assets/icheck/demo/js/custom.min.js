$(document).ready(function(){var h=window.location.hash.replace("#","");if(h&&$("."+h).length){var k=$("."+h).offset().top-40;window.Zepto?window.scrollTo(0,k):$(window).scrollTop($("."+h).offset().top-40)}$(".skin dt").click(function(){$(this).siblings().removeClass("selected").end().prev("dd").andSelf().addClass("selected")});$(".arrows .top, .arrows .bottom, .features .self, .skins-info .self, .usage .self").click(function(b){var a=$(this).data("to"),d=$("."+a).offset().top;b.preventDefault();
window.location.hash=a;window.Zepto?window.scrollTo(0,d-40):$("html, body").stop().animate({scrollTop:d-40},600)});$(".colors li").click(function(){var b=$(this);if(!b.hasClass("active")){b.siblings().removeClass("active");var a=b.closest(".skin"),d=b.attr("class")?"-"+b.attr("class"):"",c=a.data("icheckbox"),g=a.data("iradio"),e="icheckbox_minimal",f="iradio_minimal";a.hasClass("skin-square")&&(e="icheckbox_square",f="iradio_square",void 0==c&&(c="icheckbox_square-green",g="iradio_square-green"));
a.hasClass("skin-flat")&&(e="icheckbox_flat",f="iradio_flat",void 0==c&&(c="icheckbox_flat-red",g="iradio_flat-red"));a.hasClass("skin-line")&&(e="icheckbox_line",f="iradio_line",void 0==c&&(c="icheckbox_line-blue",g="iradio_line-blue"));void 0==c&&(c=e,g=f);a.find("input, .skin-states .state").each(function(){var a=$(this).hasClass("state")?$(this):$(this).parent(),b=a.attr("class").replace(c,e+d).replace(g,f+d);a.attr("class",b)});a.data("icheckbox",e+d);a.data("iradio",f+d);b.addClass("active")}});
$(".demo-methods dt .self").click(function(){var b=$(this);switch(b.attr("class").replace("self ","")){case "do-check":$("#input-1, #input-3").iCheck("check");break;case "do-uncheck":$("#input-1, #input-3").iCheck("uncheck");break;case "do-disable":$("#input-2, #input-4").iCheck("disable");break;case "do-enable":$("#input-2, #input-4").iCheck("enable");break;case "do-destroy":$(".demo-list input").iCheck("destroy");break;default:var a=b.hasClass("active")?"show code":"hide code";b.toggleClass("active").text(a);
window.Zepto?$(this).closest("dt").next().toggle():$(this).closest("dt").next().slideToggle(200)}})});