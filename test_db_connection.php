<?php
// Test database connection for CloudPanel migration
echo "<h2>Database Connection Test</h2>";

// Load database config
$config_file = 'appp/mvc/config/production/database.php';
if (file_exists($config_file)) {
    include $config_file;
    echo "<p style='color: green;'>✓ Database config file found</p>";
} else {
    echo "<p style='color: red;'>✗ Database config file not found</p>";
    exit;
}

// Test database connection
$hostname = $db['default']['hostname'];
$username = $db['default']['username'];
$password = $db['default']['password'];
$database = $db['default']['database'];

echo "<h3>Connection Details:</h3>";
echo "Host: " . $hostname . "<br>";
echo "Username: " . $username . "<br>";
echo "Database: " . $database . "<br>";

// Test connection
$connection = new mysqli($hostname, $username, $password, $database);

if ($connection->connect_error) {
    echo "<p style='color: red;'>✗ Connection failed: " . $connection->connect_error . "</p>";
} else {
    echo "<p style='color: green;'>✓ Database connection successful</p>";
    
    // Test if school_sessions table exists
    $result = $connection->query("SHOW TABLES LIKE 'school_sessions'");
    if ($result->num_rows > 0) {
        echo "<p style='color: green;'>✓ school_sessions table exists</p>";
        
        // Check table structure
        $result = $connection->query("DESCRIBE school_sessions");
        if ($result) {
            echo "<h4>Table Structure:</h4>";
            echo "<table border='1'>";
            echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
            while ($row = $result->fetch_assoc()) {
                echo "<tr>";
                echo "<td>" . $row['Field'] . "</td>";
                echo "<td>" . $row['Type'] . "</td>";
                echo "<td>" . $row['Null'] . "</td>";
                echo "<td>" . $row['Key'] . "</td>";
                echo "</tr>";
            }
            echo "</table>";
        }
        
        // Check if there are any sessions
        $result = $connection->query("SELECT COUNT(*) as count FROM school_sessions");
        if ($result) {
            $row = $result->fetch_assoc();
            echo "<p>Current sessions in table: " . $row['count'] . "</p>";
        }
        
        // Test session table permissions
        $test_id = 'test_' . time();
        $test_data = 'test_data';
        $test_query = "INSERT INTO school_sessions (id, ip_address, timestamp, data) VALUES ('$test_id', '127.0.0.1', " . time() . ", '$test_data')";
        
        if ($connection->query($test_query)) {
            echo "<p style='color: green;'>✓ Can write to school_sessions table</p>";
            // Clean up test data
            $connection->query("DELETE FROM school_sessions WHERE id = '$test_id'");
        } else {
            echo "<p style='color: red;'>✗ Cannot write to school_sessions table: " . $connection->error . "</p>";
        }
        
    } else {
        echo "<p style='color: red;'>✗ school_sessions table does not exist</p>";
        echo "<p>Creating school_sessions table...</p>";
        
        // Create the table
        $create_table = "CREATE TABLE `school_sessions` (
            `id` varchar(128) NOT NULL,
            `ip_address` varchar(45) NOT NULL,
            `timestamp` int(10) unsigned DEFAULT 0 NOT NULL,
            `data` blob NOT NULL,
            KEY `ci_sessions_timestamp` (`timestamp`)
        )";
        
        if ($connection->query($create_table)) {
            echo "<p style='color: green;'>✓ school_sessions table created successfully</p>";
        } else {
            echo "<p style='color: red;'>✗ Failed to create school_sessions table: " . $connection->error . "</p>";
        }
    }
    
    $connection->close();
}

echo "<hr>";
echo "<h3>CloudPanel Migration Notes:</h3>";
echo "<ul>";
echo "<li>Check if database user has proper permissions</li>";
echo "<li>Verify database host (might be different in CloudPanel)</li>";
echo "<li>Check if session table was migrated properly</li>";
echo "<li>Verify file permissions for session files</li>";
echo "</ul>";

echo "<p><a href='test_redirect.php'>Back to Redirect Test</a></p>";
?>
