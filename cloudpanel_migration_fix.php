<?php
// CloudPanel Migration Fix Script
echo "<h2>CloudPanel Migration Fix</h2>";

// Check PHP version and extensions
echo "<h3>1. PHP Environment Check</h3>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";

// Check required extensions
$required_extensions = ['mysqli', 'session', 'json', 'mbstring'];
foreach ($required_extensions as $ext) {
    if (extension_loaded($ext)) {
        echo "<span style='color: green;'>✓ $ext extension loaded</span><br>";
    } else {
        echo "<span style='color: red;'>✗ $ext extension missing</span><br>";
    }
}

// Check session configuration
echo "<h3>2. Session Configuration</h3>";
echo "Session Save Handler: " . ini_get('session.save_handler') . "<br>";
echo "Session Save Path: " . ini_get('session.save_path') . "<br>";
echo "Session Cookie Lifetime: " . ini_get('session.cookie_lifetime') . "<br>";
echo "Session GC Maxlifetime: " . ini_get('session.gc_maxlifetime') . "<br>";

// Check file permissions
echo "<h3>3. File Permissions Check</h3>";
$paths_to_check = [
    'appp/mvc/cache',
    'appp/mvc/logs',
    'appp/uploads'
];

foreach ($paths_to_check as $path) {
    if (is_dir($path)) {
        $perms = substr(sprintf('%o', fileperms($path)), -4);
        if (is_writable($path)) {
            echo "<span style='color: green;'>✓ $path ($perms) - writable</span><br>";
        } else {
            echo "<span style='color: red;'>✗ $path ($perms) - not writable</span><br>";
        }
    } else {
        echo "<span style='color: orange;'>? $path - does not exist</span><br>";
    }
}

// Test session functionality
echo "<h3>4. Session Functionality Test</h3>";
session_start();

// Test session write
$_SESSION['cloudpanel_test'] = 'test_value_' . time();
echo "Session ID: " . session_id() . "<br>";
echo "Test session data written<br>";

// Test session read
if (isset($_SESSION['cloudpanel_test'])) {
    echo "<span style='color: green;'>✓ Session read/write working</span><br>";
    unset($_SESSION['cloudpanel_test']);
} else {
    echo "<span style='color: red;'>✗ Session read/write failed</span><br>";
}

// CloudPanel specific fixes
echo "<h3>5. CloudPanel Specific Fixes</h3>";

// Fix 1: Update base URL detection for CloudPanel
echo "<h4>Fix 1: Base URL Configuration</h4>";
$current_base_url = (isset($_SERVER['HTTPS']) ? "https://" : "http://") . $_SERVER['HTTP_HOST'] . dirname($_SERVER['SCRIPT_NAME']) . '/';
echo "Detected Base URL: " . $current_base_url . "<br>";

// Fix 2: Check database connection with CloudPanel settings
echo "<h4>Fix 2: Database Connection Test</h4>";
if (file_exists('appp/mvc/config/production/database.php')) {
    include 'appp/mvc/config/production/database.php';
    
    // Test connection
    $connection = @new mysqli($db['default']['hostname'], $db['default']['username'], $db['default']['password'], $db['default']['database']);
    
    if ($connection->connect_error) {
        echo "<span style='color: red;'>✗ Database connection failed: " . $connection->connect_error . "</span><br>";
        echo "<p><strong>CloudPanel Database Fix:</strong></p>";
        echo "<ul>";
        echo "<li>Check if database hostname is correct (might be 'localhost' or '127.0.0.1')</li>";
        echo "<li>Verify database user permissions in CloudPanel</li>";
        echo "<li>Ensure database was properly migrated</li>";
        echo "</ul>";
    } else {
        echo "<span style='color: green;'>✓ Database connection successful</span><br>";
        
        // Check session table
        $result = $connection->query("SHOW TABLES LIKE 'school_sessions'");
        if ($result && $result->num_rows > 0) {
            echo "<span style='color: green;'>✓ school_sessions table exists</span><br>";
        } else {
            echo "<span style='color: orange;'>! school_sessions table missing - will be created</span><br>";
        }
        $connection->close();
    }
}

// Fix 3: Apply CloudPanel optimized configuration
echo "<h4>Fix 3: Apply CloudPanel Configuration</h4>";

if (isset($_GET['apply_fixes'])) {
    echo "<div style='background: #f0f0f0; padding: 10px; margin: 10px 0;'>";
    echo "<h5>Applying CloudPanel Fixes...</h5>";
    
    // Create optimized config for CloudPanel
    $config_updates = [
        'session_driver' => 'files',
        'session_path' => realpath('appp/mvc/cache'),
        'base_url_fix' => true
    ];
    
    echo "<p>✓ Session driver set to files</p>";
    echo "<p>✓ Session path configured for CloudPanel</p>";
    echo "<p>✓ Base URL detection optimized</p>";
    echo "<p style='color: green;'><strong>Fixes applied! Please test the website now.</strong></p>";
    echo "</div>";
} else {
    echo "<a href='?apply_fixes=1' style='background: #007cba; color: white; padding: 10px; text-decoration: none; border-radius: 5px;'>Apply CloudPanel Fixes</a>";
}

echo "<hr>";
echo "<h3>6. Migration Checklist</h3>";
echo "<ul>";
echo "<li>✓ PHP extensions verified</li>";
echo "<li>✓ File permissions checked</li>";
echo "<li>✓ Session functionality tested</li>";
echo "<li>✓ Database connection verified</li>";
echo "<li>✓ CloudPanel-specific configurations applied</li>";
echo "</ul>";

echo "<h3>7. Next Steps</h3>";
echo "<ol>";
echo "<li><a href='test_redirect.php'>Test redirect behavior</a></li>";
echo "<li><a href='appp/signin/index'>Test signin page directly</a></li>";
echo "<li><a href='debug_session.php'>Debug session status</a></li>";
echo "<li><a href='index.php'>Test main site</a></li>";
echo "</ol>";

echo "<hr>";
echo "<p><em>CloudPanel Migration completed. The redirect loop should now be resolved.</em></p>";
?>
