<?php
// Test script to check redirect behavior
echo "<h2>Redirect Test</h2>";
echo "<p>Testing the redirect behavior...</p>";

// Test 1: Check if we can access the signin page directly
echo "<h3>Test 1: Direct access to signin</h3>";
echo "<a href='appp/signin/index' target='_blank'>Test Signin Page</a><br>";

// Test 2: Check if we can access the dashboard (should redirect to signin if not logged in)
echo "<h3>Test 2: Direct access to dashboard</h3>";
echo "<a href='appp/dashboard/index' target='_blank'>Test Dashboard Page</a><br>";

// Test 3: Check session status
echo "<h3>Test 3: Session Status</h3>";
session_start();
echo "Session ID: " . session_id() . "<br>";
echo "Session Status: " . session_status() . "<br>";
echo "Session Data: <pre>" . print_r($_SESSION, true) . "</pre>";

// Test 4: Clear session and test
echo "<h3>Test 4: Clear Session</h3>";
if (isset($_GET['clear'])) {
    session_destroy();
    echo "<p style='color: green;'>Session destroyed!</p>";
    echo "<a href='test_redirect.php'>Refresh</a>";
} else {
    echo "<a href='test_redirect.php?clear=1'>Clear Session and Test</a>";
}

echo "<hr>";
echo "<h3>Navigation Links:</h3>";
echo "<a href='index.php'>Root Index</a> | ";
echo "<a href='debug_session.php'>Debug Session</a> | ";
echo "<a href='appp/signin/index'>Signin</a>";
?>
